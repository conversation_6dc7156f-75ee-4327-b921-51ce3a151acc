<?xml version="1.0" encoding="UTF-8"?>
<project version="4">
  <component name="ExternalStorageConfigurationManager" enabled="true" />
  <component name="MavenProjectsManager">
    <option name="originalFiles">
      <list>
        <option value="$PROJECT_DIR$/xiaozhi-backend-java/pom.xml" />
        <option value="$PROJECT_DIR$/xiaozhi-backend-simple/pom.xml" />
        <option value="$PROJECT_DIR$/esp32-stt-backend/pom.xml" />
        <option value="$PROJECT_DIR$/esp32-backend/pom.xml" />
        <option value="$PROJECT_DIR$/esp32-backend-new/pom.xml" />
      </list>
    </option>
    <option name="ignoredFiles">
      <set>
        <option value="$PROJECT_DIR$/esp32-backend/pom.xml" />
      </set>
    </option>
  </component>
  <component name="ProjectRootManager" version="2" project-jdk-name="openjdk-23" project-jdk-type="JavaSDK">
    <output url="file://$PROJECT_DIR$/out" />
  </component>
</project>