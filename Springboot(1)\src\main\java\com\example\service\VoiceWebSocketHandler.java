package com.example.service;

import com.fasterxml.jackson.databind.ObjectMapper;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.web.socket.TextMessage;
import org.springframework.web.socket.BinaryMessage;
import org.springframework.web.socket.WebSocketSession;
import org.springframework.web.socket.CloseStatus;
import org.springframework.web.socket.handler.TextWebSocketHandler;

import jakarta.annotation.PreDestroy;
import java.io.IOException;
import java.util.Map;
import java.util.HashMap;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.atomic.AtomicInteger;

/**
 * 处理与网页前端的WebSocket通信
 */
@Component
public class VoiceWebSocketHandler extends TextWebSocketHandler {

    private final SensevoiceService sensevoiceService;
    private final VllmService vllmService;
    private final CosyvoiceService cosyvoiceService;
    private final ObjectMapper objectMapper;
    
    // 使用线程池处理请求，避免阻塞WebSocket线程
    private final ExecutorService executorService = Executors.newCachedThreadPool();
    
    // 存储活跃的WebSocket会话
    private final Map<String, WebSocketSession> sessions = new ConcurrentHashMap<>();
    
    // 为每个会话分配一个唯一ID
    private final AtomicInteger sessionCounter = new AtomicInteger(0);

    @Autowired
    public VoiceWebSocketHandler(SensevoiceService sensevoiceService, VllmService vllmService,
                                 CosyvoiceService cosyvoiceService) {
        this.sensevoiceService = sensevoiceService;
        this.vllmService = vllmService;
        this.cosyvoiceService = cosyvoiceService;
        this.objectMapper = new ObjectMapper();
        
        System.out.println("WebSocket处理器初始化完成");
    }

    @Override
    public void afterConnectionEstablished(WebSocketSession session) throws Exception {
        // 保存会话
        String sessionId = String.valueOf(sessionCounter.incrementAndGet());
        sessions.put(sessionId, session);
        session.getAttributes().put("sessionId", sessionId);
        
        System.out.println("WebSocket连接已建立: " + sessionId);
        
        // 发送欢迎消息
        sendMessage(session, "connected", Map.of("message", "连接成功", "sessionId", sessionId));
    }
    
    @Override
    public void afterConnectionClosed(WebSocketSession session, CloseStatus status) throws Exception {
        // 从会话映射中移除
        String sessionId = (String) session.getAttributes().get("sessionId");
        sessions.remove(sessionId);
        
        System.out.println("WebSocket连接已关闭: " + sessionId + ", 状态: " + status);
    }
    
    @Override
    public void handleTransportError(WebSocketSession session, Throwable exception) throws Exception {
        System.err.println("WebSocket传输错误: " + session.getId() + ", 错误: " + exception.getMessage());
        exception.printStackTrace();
        
        try {
            // 尝试发送错误消息
            sendMessage(session, "error", Map.of("message", "WebSocket连接出错: " + exception.getMessage()));
        } catch (Exception e) {
            // 忽略错误消息发送失败
        }
        
        // 关闭会话
        if (session.isOpen()) {
            session.close(CloseStatus.SERVER_ERROR);
        }
        
        // 从活跃会话中删除
        sessions.remove(session.getId());
    }

    @Override
    protected void handleTextMessage(WebSocketSession session, TextMessage message) throws Exception {
        String payload = message.getPayload();
        System.out.println("收到WebSocket消息: " + payload);
        
        try {
            // 解析JSON消息
            @SuppressWarnings("unchecked")
            Map<String, Object> request = objectMapper.readValue(payload, Map.class);
            String type = (String) request.get("type");
            
            // 根据消息类型处理
            if ("text".equals(type)) {
                handleTextRequest(session, request);
            } else if ("audio".equals(type)) {
                handleAudioRequest(session, request);
            } else if ("clear_history".equals(type)) {
                handleClearHistoryRequest(session);
            } else {
                sendErrorMessage(session, "不支持的消息类型: " + type);
            }
        } catch (Exception e) {
            System.err.println("处理消息失败: " + e.getMessage());
            e.printStackTrace();
            sendErrorMessage(session, "处理消息失败: " + e.getMessage());
        }
    }
    
    /**
     * 处理文本请求
     */
    private void handleTextRequest(WebSocketSession session, Map<String, Object> request) {
        // 获取文本内容
        String text = (String) request.get("content");
        if (text == null || text.isEmpty()) {
            try {
                sendErrorMessage(session, "文本内容为空");
            } catch (IOException e) {
                System.err.println("发送错误消息失败: " + e.getMessage());
            }
            return;
        }
        
        // 在单独线程中处理，避免阻塞WebSocket线程
        executorService.submit(() -> {
            try {
                // 调用VLLM处理文本
                sendMessage(session, "processing", Map.of("message", "正在处理您的请求..."));
                
                String response = vllmService.process(text);
                
                // 发送文本响应
                sendMessage(session, "text_response", Map.of(
                    "text", text,
                    "response", response
                ));
                
                // 生成语音响应（可选）
                boolean generateSpeech = true; // 可以根据客户端请求决定是否生成语音
                if (generateSpeech) {
                    // 获取默认音色
                    String[] speakers = cosyvoiceService.getSpeakers();
                    String speaker = speakers.length > 0 ? speakers[0] : "default";
                    
                    // 生成语音
                    byte[] audioData = cosyvoiceService.generateSpeech(response, speaker);
                    
                    // 发送音频响应（Base64编码）
                    if (audioData != null && audioData.length > 0) {
                        String base64Audio = java.util.Base64.getEncoder().encodeToString(audioData);
                        sendMessage(session, "audio_response", Map.of(
                            "audio", base64Audio,
                            "format", "pcm",
                            "sampleRate", 16000,
                            "channels", 1
                        ));
                    }
                }
            } catch (Exception e) {
                System.err.println("处理文本请求失败: " + e.getMessage());
                e.printStackTrace();
                try {
                    sendErrorMessage(session, "处理请求失败: " + e.getMessage());
                } catch (IOException ex) {
                    System.err.println("发送错误消息失败: " + ex.getMessage());
                }
            }
        });
    }
    
    /**
     * 处理音频请求
     */
    private void handleAudioRequest(WebSocketSession session, Map<String, Object> request) {
        // 获取Base64编码的音频数据
        String base64Audio = (String) request.get("audio");
        if (base64Audio == null || base64Audio.isEmpty()) {
            try {
                sendErrorMessage(session, "音频数据为空");
            } catch (IOException e) {
                System.err.println("发送错误消息失败: " + e.getMessage());
            }
            return;
        }
        
        // 在单独线程中处理，避免阻塞WebSocket线程
        executorService.submit(() -> {
            try {
                // 解码Base64音频数据
                byte[] audioData = java.util.Base64.getDecoder().decode(base64Audio);
                
                // 发送处理中消息
                sendMessage(session, "processing", Map.of("message", "正在处理音频..."));
                
                // 调用语音识别服务
                String recognizedText = sensevoiceService.recognizeSpeech(audioData);
                if (recognizedText == null || recognizedText.isEmpty()) {
                    sendMessage(session, "error", Map.of("message", "无法识别语音"));
                    return;
                }
                
                // 发送识别结果
                sendMessage(session, "recognition_result", Map.of("text", recognizedText));
                
                // 调用VLLM处理文本
                String response = vllmService.process(recognizedText);
                
                // 发送文本响应
                sendMessage(session, "text_response", Map.of(
                    "text", recognizedText,
                    "response", response
                ));
                
                // 生成语音响应
                // 获取默认音色
                String[] speakers = cosyvoiceService.getSpeakers();
                String speaker = speakers.length > 0 ? speakers[0] : "default";
                
                // 生成语音
                byte[] speechData = cosyvoiceService.generateSpeech(response, speaker);
                
                // 发送音频响应（Base64编码）
                if (speechData != null && speechData.length > 0) {
                    String base64Speech = java.util.Base64.getEncoder().encodeToString(speechData);
                    sendMessage(session, "audio_response", Map.of(
                        "audio", base64Speech,
                        "format", "pcm",
                        "sampleRate", 16000,
                        "channels", 1
                    ));
                }
            } catch (Exception e) {
                System.err.println("处理音频请求失败: " + e.getMessage());
                e.printStackTrace();
                try {
                    sendErrorMessage(session, "处理音频失败: " + e.getMessage());
                } catch (IOException ex) {
                    System.err.println("发送错误消息失败: " + ex.getMessage());
                }
            }
        });
    }
    
    /**
     * 处理清除历史请求
     */
    private void handleClearHistoryRequest(WebSocketSession session) {
        executorService.submit(() -> {
            try {
                // 清除历史
                vllmService.clearHistory();
                
                // 发送响应
                sendMessage(session, "history_cleared", Map.of("message", "对话历史已清除"));
                
                System.out.println("对话历史已清除");
            } catch (Exception e) {
                System.err.println("清除历史失败: " + e.getMessage());
                e.printStackTrace();
                try {
                    sendErrorMessage(session, "清除历史失败: " + e.getMessage());
                } catch (IOException ex) {
                    System.err.println("发送错误消息失败: " + ex.getMessage());
                }
            }
        });
    }
    
    /**
     * 发送错误消息
     */
    private void sendErrorMessage(WebSocketSession session, String errorMessage) throws IOException {
        sendMessage(session, "error", Map.of("message", errorMessage));
    }
    
    /**
     * 发送消息到WebSocket客户端
     */
    private void sendMessage(WebSocketSession session, String type, Map<String, Object> data) throws IOException {
        if (!session.isOpen()) {
            throw new IOException("WebSocket会话已关闭");
        }
        
        try {
            Map<String, Object> response = new java.util.HashMap<>();
            response.put("type", type);
            response.putAll(data);
            
            String jsonResponse = objectMapper.writeValueAsString(response);
            session.sendMessage(new TextMessage(jsonResponse));
        } catch (Exception e) {
            System.err.println("发送WebSocket消息失败: " + e.getMessage());
            throw new IOException("发送消息失败", e);
        }
    }
    
    /**
     * 在服务关闭时释放资源
     */
    @PreDestroy
    public void destroy() {
        System.out.println("正在关闭WebSocket处理器...");
        
        // 关闭所有会话
        for (WebSocketSession session : sessions.values()) {
            try {
                if (session.isOpen()) {
                    session.close(CloseStatus.NORMAL);
                }
            } catch (Exception e) {
                System.err.println("关闭WebSocket会话失败: " + e.getMessage());
            }
        }
        
        // 清空映射
        sessions.clear();
        
        // 关闭线程池
        executorService.shutdown();
        try {
            if (!executorService.awaitTermination(5, TimeUnit.SECONDS)) {
                executorService.shutdownNow();
            }
        } catch (InterruptedException e) {
            executorService.shutdownNow();
            Thread.currentThread().interrupt();
        }
        
        System.out.println("WebSocket处理器已关闭");
    }
    
    /**
     * WebSocket消息发送器，用于在其他服务中发送WebSocket消息
     */
    public interface WebSocketMessageSender {
        void sendWebSocketMessage(String type, Map<String, Object> data) throws IOException;
        
        // 添加发送消息的方法
        void sendMessage(String type, String message) throws IOException;
        
        // 添加完成方法
        void complete() throws IOException;
    }
} 