package com.example.config;

import org.springframework.context.annotation.Configuration;
import org.springframework.web.socket.config.annotation.EnableWebSocket;
import org.springframework.web.socket.config.annotation.WebSocketConfigurer;
import org.springframework.web.socket.config.annotation.WebSocketHandlerRegistry;
import org.springframework.web.socket.server.standard.ServletServerContainerFactoryBean;
import org.springframework.context.annotation.Bean;
import org.springframework.beans.factory.annotation.Autowired;
import com.example.service.VoiceWebSocketHandler;
import com.example.service.DeviceWebSocketHandler;

@Configuration
@EnableWebSocket
public class WebSocketConfig implements WebSocketConfigurer {

    private final VoiceWebSocketHandler voiceWebSocketHandler;
    private final DeviceWebSocketHandler deviceWebSocketHandler;

    @Autowired
    public WebSocketConfig(VoiceWebSocketHandler voiceWebSocketHandler, 
                          DeviceWebSocketHandler deviceWebSocketHandler) {
        this.voiceWebSocketHandler = voiceWebSocketHandler;
        this.deviceWebSocketHandler = deviceWebSocketHandler;
    }

    @Override
    public void registerWebSocketHandlers(WebSocketHandlerRegistry registry) {
        // 网页端WebSocket端点
        registry.addHandler(voiceWebSocketHandler, "/api/voice/ws")
                .setAllowedOrigins("*"); // 允许所有来源的WebSocket连接
                
        // ESP32设备WebSocket端点
        registry.addHandler(deviceWebSocketHandler, "/xiaozhi/v1")
                .setAllowedOrigins("*"); // 允许所有来源的WebSocket连接
                
        // 添加兼容客户端的WebSocket端点
        registry.addHandler(deviceWebSocketHandler, "/ws/chat")
                .setAllowedOrigins("*"); // 允许所有来源的WebSocket连接
                
        // 添加客户端配置中的WebSocket端点
        registry.addHandler(deviceWebSocketHandler, "/ws")
                .setAllowedOrigins("*"); // 允许所有来源的WebSocket连接
    }
    
    /**
     * 配置WebSocket容器，设置消息大小限制
     */
    @Bean
    public ServletServerContainerFactoryBean createWebSocketContainer() {
        ServletServerContainerFactoryBean container = new ServletServerContainerFactoryBean();
        // 设置文本消息大小限制为10MB
        container.setMaxTextMessageBufferSize(10 * 1024 * 1024);
        // 设置二进制消息大小限制为10MB
        container.setMaxBinaryMessageBufferSize(10 * 1024 * 1024);
        return container;
    }
} 