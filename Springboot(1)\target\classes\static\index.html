<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>智能语音对话测试</title>
    <style>
        body {
            font-family: 'Microsoft YaHei', Arial, sans-serif;
            margin: 0;
            padding: 20px;
            background-color: #f5f5f5;
            color: #333;
        }
        .container {
            max-width: 800px;
            margin: 0 auto;
            background-color: #fff;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
        }
        h1 {
            color: #2c3e50;
            text-align: center;
            margin-bottom: 20px;
        }
        .status {
            padding: 10px;
            margin: 10px 0;
            border-radius: 4px;
            font-weight: bold;
        }
        .connected {
            background-color: #dff0d8;
            color: #3c763d;
        }
        .disconnected {
            background-color: #f2dede;
            color: #a94442;
        }
        .connecting {
            background-color: #fcf8e3;
            color: #8a6d3b;
        }
        .controls {
            margin: 20px 0;
            display: flex;
            gap: 10px;
            align-items: center;
        }
        button {
            padding: 10px 15px;
            border: none;
            border-radius: 4px;
            cursor: pointer;
            background-color: #3498db;
            color: white;
            font-size: 16px;
            transition: background-color 0.3s;
        }
        button:hover {
            background-color: #2980b9;
        }
        button:disabled {
            background-color: #bdc3c7;
            cursor: not-allowed;
        }
        .record-btn {
            background-color: #e74c3c;
        }
        .record-btn:hover {
            background-color: #c0392b;
        }
        .record-btn.recording {
            animation: pulse 1.5s infinite;
        }
        @keyframes pulse {
            0% { background-color: #e74c3c; }
            50% { background-color: #c0392b; }
            100% { background-color: #e74c3c; }
        }
        textarea {
            width: 100%;
            padding: 10px;
            border-radius: 4px;
            border: 1px solid #ddd;
            margin-bottom: 10px;
            font-size: 16px;
            height: 100px;
            resize: vertical;
        }
        .conversation {
            margin-top: 20px;
            border: 1px solid #ddd;
            border-radius: 4px;
            padding: 10px;
            max-height: 300px;
            overflow-y: auto;
        }
        .message {
            margin-bottom: 10px;
            padding: 8px;
            border-radius: 4px;
        }
        .user {
            background-color: #e8f4f8;
            margin-right: 20px;
            margin-left: 0;
        }
        .assistant {
            background-color: #f0f0f0;
            margin-left: 20px;
            margin-right: 0;
        }
        .speech-controls {
            display: flex;
            gap: 10px;
            margin-top: 10px;
            justify-content: flex-end;
        }
        .audio-player {
            display: none;
            width: 100%;
            margin-top: 10px;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>智能语音对话测试</h1>
        
        <div id="connectionStatus" class="status disconnected">
            未连接
        </div>
        
        <div class="controls">
            <button id="connectBtn">连接服务器</button>
            <button id="disconnectBtn" disabled>断开连接</button>
            <button id="recordBtn" class="record-btn" disabled>开始录音</button>
            <button id="clearBtn" disabled>清除历史</button>
        </div>
        
        <div>
            <textarea id="textInput" placeholder="在此输入文本..." disabled></textarea>
            <button id="sendTextBtn" disabled>发送文本</button>
        </div>
        
        <div class="conversation" id="conversation">
            <div class="message assistant">
                你好，我是智能语音助手，请问有什么可以帮助你的吗？
            </div>
        </div>
        
        <audio id="audioPlayer" class="audio-player" controls></audio>
    </div>

    <script>
        // 全局变量
        let ws = null;
        let isRecording = false;
        let mediaRecorder = null;
        let audioChunks = [];
        
        // DOM 元素
        const connectBtn = document.getElementById('connectBtn');
        const disconnectBtn = document.getElementById('disconnectBtn');
        const recordBtn = document.getElementById('recordBtn');
        const clearBtn = document.getElementById('clearBtn');
        const textInput = document.getElementById('textInput');
        const sendTextBtn = document.getElementById('sendTextBtn');
        const conversation = document.getElementById('conversation');
        const connectionStatus = document.getElementById('connectionStatus');
        const audioPlayer = document.getElementById('audioPlayer');
        
        // 连接WebSocket
        connectBtn.addEventListener('click', () => {
            connectWebSocket();
        });
        
        // 断开WebSocket连接
        disconnectBtn.addEventListener('click', () => {
            if (ws) {
                ws.close();
            }
        });
        
        // 录音按钮点击事件
        recordBtn.addEventListener('click', () => {
            if (isRecording) {
                stopRecording();
            } else {
                startRecording();
            }
        });
        
        // 发送文本按钮点击事件
        sendTextBtn.addEventListener('click', () => {
            const text = textInput.value.trim();
            if (text && ws) {
                sendTextMessage(text);
                textInput.value = '';
            }
        });
        
        // 清除历史按钮点击事件
        clearBtn.addEventListener('click', () => {
            if (ws) {
                sendClearHistoryMessage();
            }
        });
        
        // 连接WebSocket服务器
        function connectWebSocket() {
            if (ws) {
                ws.close();
            }
            
            // 更新状态
            connectionStatus.className = 'status connecting';
            connectionStatus.textContent = '正在连接...';
            
            // 创建WebSocket连接
            const protocol = window.location.protocol === 'https:' ? 'wss:' : 'ws:';
            const wsUrl = `${protocol}//${window.location.host}/api/voice/ws`;
            
            ws = new WebSocket(wsUrl);
            
            // 连接建立时
            ws.onopen = () => {
                connectionStatus.className = 'status connected';
                connectionStatus.textContent = '已连接';
                
                connectBtn.disabled = true;
                disconnectBtn.disabled = false;
                recordBtn.disabled = false;
                textInput.disabled = false;
                sendTextBtn.disabled = false;
                clearBtn.disabled = false;
                
                console.log('WebSocket连接已建立');
            };
            
            // 收到消息时
            ws.onmessage = (event) => {
                console.log('收到消息:', event.data);
                handleWebSocketMessage(JSON.parse(event.data));
            };
            
            // 连接关闭时
            ws.onclose = () => {
                connectionStatus.className = 'status disconnected';
                connectionStatus.textContent = '已断开连接';
                
                connectBtn.disabled = false;
                disconnectBtn.disabled = true;
                recordBtn.disabled = true;
                textInput.disabled = true;
                sendTextBtn.disabled = true;
                clearBtn.disabled = true;
                
                // 如果正在录音，停止录音
                if (isRecording) {
                    stopRecording();
                }
                
                ws = null;
                console.log('WebSocket连接已关闭');
            };
            
            // 连接错误时
            ws.onerror = (error) => {
                console.error('WebSocket错误:', error);
                connectionStatus.className = 'status disconnected';
                connectionStatus.textContent = '连接错误';
            };
        }
        
        // 处理WebSocket消息
        function handleWebSocketMessage(message) {
            switch (message.type) {
                case 'connected':
                    console.log('连接成功，会话ID:', message.sessionId);
                    break;
                    
                case 'processing':
                    addMessage('正在处理...', 'assistant');
                    break;
                    
                case 'recognition_result':
                    addMessage(message.text, 'user');
                    break;
                    
                case 'text_response':
                    // 更新最后一条消息或添加新消息
                    const lastMessage = conversation.lastElementChild;
                    if (lastMessage && lastMessage.textContent.trim() === '正在处理...') {
                        lastMessage.textContent = message.response;
                    } else {
                        addMessage(message.response, 'assistant');
                    }
                    break;
                    
                case 'audio_response':
                    playAudio(message.audio);
                    break;
                    
                case 'error':
                    console.error('错误:', message.message);
                    addMessage(`错误: ${message.message}`, 'assistant');
                    break;
                    
                default:
                    console.log('未处理的消息类型:', message.type);
            }
        }
        
        // 添加消息到对话框
        function addMessage(text, type) {
            const messageDiv = document.createElement('div');
            messageDiv.className = `message ${type}`;
            messageDiv.textContent = text;
            
            conversation.appendChild(messageDiv);
            conversation.scrollTop = conversation.scrollHeight;
        }
        
        // 开始录音
        function startRecording() {
            if (!navigator.mediaDevices || !navigator.mediaDevices.getUserMedia) {
                alert('您的浏览器不支持录音功能');
                return;
            }
            
            navigator.mediaDevices.getUserMedia({ audio: true })
                .then((stream) => {
                    isRecording = true;
                    recordBtn.textContent = '停止录音';
                    recordBtn.classList.add('recording');
                    
                    audioChunks = [];
                    mediaRecorder = new MediaRecorder(stream);
                    
                    mediaRecorder.ondataavailable = (event) => {
                        if (event.data.size > 0) {
                            audioChunks.push(event.data);
                        }
                    };
                    
                    mediaRecorder.onstop = () => {
                        const audioBlob = new Blob(audioChunks, { type: 'audio/wav' });
                        processAudioData(audioBlob);
                    };
                    
                    mediaRecorder.start();
                })
                .catch((error) => {
                    console.error('获取麦克风权限失败:', error);
                    alert('无法访问麦克风，请检查权限设置');
                });
        }
        
        // 停止录音
        function stopRecording() {
            if (mediaRecorder && isRecording) {
                mediaRecorder.stop();
                
                isRecording = false;
                recordBtn.textContent = '开始录音';
                recordBtn.classList.remove('recording');
                
                // 关闭麦克风轨道
                if (mediaRecorder.stream) {
                    mediaRecorder.stream.getTracks().forEach(track => track.stop());
                }
            }
        }
        
        // 处理录音数据
        function processAudioData(audioBlob) {
            // 读取音频数据
            const reader = new FileReader();
            reader.onload = () => {
                // 将ArrayBuffer转为Base64
                const base64Audio = btoa(
                    new Uint8Array(reader.result)
                        .reduce((data, byte) => data + String.fromCharCode(byte), '')
                );
                
                // 发送音频数据
                if (ws && ws.readyState === WebSocket.OPEN) {
                    ws.send(JSON.stringify({
                        type: 'audio',
                        audio: base64Audio
                    }));
                }
            };
            reader.readAsArrayBuffer(audioBlob);
        }
        
        // 播放音频
        function playAudio(base64Audio) {
            if (!base64Audio) return;
            
            const audioData = atob(base64Audio);
            const arrayBuffer = new ArrayBuffer(audioData.length);
            const view = new Uint8Array(arrayBuffer);
            
            for (let i = 0; i < audioData.length; i++) {
                view[i] = audioData.charCodeAt(i);
            }
            
            const audioBlob = new Blob([arrayBuffer], { type: 'audio/wav' });
            const audioUrl = URL.createObjectURL(audioBlob);
            
            audioPlayer.src = audioUrl;
            audioPlayer.style.display = 'block';
            audioPlayer.play();
        }
        
        // 发送文本消息
        function sendTextMessage(text) {
            if (ws && ws.readyState === WebSocket.OPEN) {
                ws.send(JSON.stringify({
                    type: 'text',
                    content: text
                }));
                
                addMessage(text, 'user');
            }
        }
        
        // 发送清除历史消息
        function sendClearHistoryMessage() {
            if (ws && ws.readyState === WebSocket.OPEN) {
                ws.send(JSON.stringify({
                    type: 'clear_history'
                }));
                
                // 清空对话框，只留下初始欢迎消息
                conversation.innerHTML = '';
                addMessage('你好，我是智能语音助手，请问有什么可以帮助你的吗？', 'assistant');
            }
        }
        
        // 文本输入框按Enter发送
        textInput.addEventListener('keydown', (event) => {
            if (event.key === 'Enter' && !event.shiftKey) {
                event.preventDefault();
                sendTextBtn.click();
            }
        });
    </script>
</body>
</html> 