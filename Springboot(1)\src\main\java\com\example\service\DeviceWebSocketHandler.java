package com.example.service;

import com.fasterxml.jackson.databind.ObjectMapper;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.web.socket.TextMessage;
import org.springframework.web.socket.BinaryMessage;
import org.springframework.web.socket.WebSocketSession;
import org.springframework.web.socket.CloseStatus;
import org.springframework.web.socket.handler.TextWebSocketHandler;

import java.io.ByteArrayOutputStream;
import java.io.IOException;
import java.nio.ByteBuffer;
import java.nio.ByteOrder;
import java.util.*;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.ScheduledExecutorService;
import jakarta.annotation.PreDestroy;

import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;
import java.util.concurrent.ScheduledExecutorService;
import java.util.concurrent.TimeUnit;
import java.io.IOException;
import java.io.ByteArrayOutputStream;
import java.nio.ByteBuffer;
import java.util.UUID;
import java.util.HashMap;
import java.util.Map;
import java.util.regex.Pattern;
import java.util.regex.Matcher;
import java.util.concurrent.ConcurrentLinkedQueue;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.function.Consumer;
import java.util.concurrent.Semaphore;

/**
 * ESP32设备专用WebSocket处理器
 * 处理ESP32设备的WebSocket连接、音频数据收发以及会话管理
 */
@Component
public class DeviceWebSocketHandler extends TextWebSocketHandler {

    private final SensevoiceService sensevoiceService;
    private final VllmService vllmService;
    private final CosyvoiceService cosyvoiceService;
    private final AudioCodec audioCodec;
    private final VadService vadService;
    private final ObjectMapper objectMapper;
    
    // 使用线程池处理请求，避免阻塞WebSocket线程
    private final ExecutorService executorService = Executors.newCachedThreadPool();
    
    // 使用定时任务线程池处理延迟任务
    private final ScheduledExecutorService scheduledExecutorService = Executors.newSingleThreadScheduledExecutor();
    
    // 存储活跃的WebSocket会话
    private final Map<String, WebSocketSession> sessions = new ConcurrentHashMap<>();
    // 存储设备会话信息
    private final Map<String, DeviceSession> deviceSessions = new ConcurrentHashMap<>();

    @Autowired
    public DeviceWebSocketHandler(SensevoiceService sensevoiceService, 
                                VllmService vllmService,
                                CosyvoiceService cosyvoiceService,
                                AudioCodec audioCodec,
                                VadService vadService) {
        this.sensevoiceService = sensevoiceService;
        this.vllmService = vllmService;
        this.cosyvoiceService = cosyvoiceService;
        this.audioCodec = audioCodec;
        this.vadService = vadService;
        this.objectMapper = new ObjectMapper();
        
        System.out.println("WebSocket处理器初始化完成");
        
        // 启动定期检查长时间静音的任务，初始延迟2秒，之后每2秒检查一次
        scheduledExecutorService.scheduleAtFixedRate(this::checkInactiveSessions, 2, 2, TimeUnit.SECONDS);
        System.out.println("长时间静音检查任务已启动，每2秒检查一次");
    }
    
    /**
     * 定期检查长时间静音的会话并断开连接
     */
    private void checkInactiveSessions() {
        try {
            System.out.println("开始检查长时间静音会话...");
            int checkedSessions = 0;
            int inactiveSessions = 0;
            
            for (Map.Entry<String, WebSocketSession> entry : sessions.entrySet()) {
                String sessionId = entry.getKey();
                WebSocketSession session = entry.getValue();
                checkedSessions++;
                
                if (session.isOpen() && vadService.isSessionInitialized(sessionId)) {
                    DeviceSession deviceSession = deviceSessions.get(sessionId);
                    
                    // 只检查处于监听状态且不在处理唤醒词的会话
                    if (deviceSession != null && deviceSession.isListening() && !deviceSession.processingWakeWord) {
                        // 检查是否长时间静音
                        if (vadService.isLongSilenceDetected(sessionId)) {
                            inactiveSessions++;
                            System.out.println("定期检查: 检测到长时间静音，准备断开连接: " + sessionId);
                            
                            // 发送待机消息
                            sendStatusMessage(session, "long_silence_detected", "检测到长时间静音，设备将进入待机状态");
                            
                            // 生成并发送告别语音
                            String goodbyeText = "检测到长时间静音，我将进入待机状态";
                            try {
                                processTextToSpeech(session, deviceSession, goodbyeText);
                            } catch (Exception e) {
                                System.err.println("发送告别语音失败: " + e.getMessage());
                            }
                            
                            // 停止监听
                            deviceSession.stopListening();
                            
                            // 延迟关闭连接
                            scheduledExecutorService.schedule(() -> {
                                try {
                                    System.out.println("检测到长时间静音，主动断开WebSocket连接: " + sessionId);
                                    session.close(CloseStatus.NORMAL);
                                } catch (IOException e) {
                                    System.err.println("关闭WebSocket连接失败: " + e.getMessage());
                                    // 强制关闭连接
                                    try {
                                        session.close(CloseStatus.SESSION_NOT_RELIABLE);
                                    } catch (IOException ex) {
                                        System.err.println("强制关闭连接失败: " + ex.getMessage());
                                    }
                                }
                            }, 1000, TimeUnit.MILLISECONDS);
                        }
                    }
                }
            }
            
            System.out.println("长时间静音检查完成: 检查了 " + checkedSessions + " 个会话, 发现 " + inactiveSessions + " 个不活跃会话");
        } catch (Exception e) {
            System.err.println("检查长时间静音会话失败: " + e.getMessage());
            e.printStackTrace();
        }
    }

    @Override
    public void afterConnectionEstablished(WebSocketSession session) throws Exception {
        // 保存会话
        String sessionId = session.getId();
        sessions.put(sessionId, session);
        
        System.out.println("设备连接已建立: " + sessionId);
        
        // 创建设备会话信息
        DeviceSession deviceSession = new DeviceSession(sessionId);
        deviceSessions.put(sessionId, deviceSession);
        
        // 初始化VAD会话
        vadService.initSession(sessionId);
    }
    
    @Override
    public void afterConnectionClosed(WebSocketSession session, CloseStatus status) throws Exception {
        // 从会话映射中移除
        String sessionId = session.getId();
        sessions.remove(sessionId);
        DeviceSession deviceSession = deviceSessions.remove(sessionId);
        
        if (deviceSession != null) {
            deviceSession.cleanup();
        }
        
        // 重置VAD会话
        vadService.resetSession(sessionId);
        
        // 清理音频编解码资源
        try {
            audioCodec.cleanup(sessionId);
        } catch (Exception e) {
            System.err.println("清理音频编解码资源失败: " + e.getMessage());
        }
        
        // 重置SensevoiceService的日志标记
        try {
            sensevoiceService.resetSessionLogFlags(sessionId);
        } catch (Exception e) {
            System.err.println("重置SensevoiceService日志标记失败: " + e.getMessage());
        }
        
        System.out.println("设备连接已关闭: " + sessionId + ", 状态: " + status);
    }
    
    @Override
    public void handleTransportError(WebSocketSession session, Throwable exception) throws Exception {
        System.err.println("设备WebSocket传输错误: " + session.getId() + ", 错误: " + exception.getMessage());
        exception.printStackTrace();
        
        // 关闭会话
        if (session.isOpen()) {
            session.close(CloseStatus.SERVER_ERROR);
        }
        
        // 从活跃会话中删除
        String sessionId = session.getId();
        sessions.remove(sessionId);
        DeviceSession deviceSession = deviceSessions.remove(sessionId);
        
        if (deviceSession != null) {
            deviceSession.cleanup();
        }
    }

    @Override
    protected void handleTextMessage(WebSocketSession session, TextMessage message) throws Exception {
        String payload = message.getPayload();
        System.out.println("收到设备文本消息: " + payload);

        try {
            // 处理JSON格式的消息
            @SuppressWarnings("unchecked")
            Map<String, Object> request = objectMapper.readValue(payload, Map.class);
            String type = (String) request.get("type");
            
            DeviceSession deviceSession = deviceSessions.get(session.getId());
            if (deviceSession == null) {
                System.err.println("未找到设备会话: " + session.getId());
                sendErrorMessage(session, "会话无效");
                return;
            }
            
            // 根据消息类型处理
            if ("hello".equals(type)) {
                // 处理hello消息（设备初始化连接）
                handleHelloMessage(session, deviceSession, request);
            } else if ("listen".equals(type)) {
                // 处理listen消息（开始/停止监听）
                handleListenMessage(session, deviceSession, request);
            } else if ("goodbye".equals(type)) {
                // 处理goodbye消息（关闭会话）
                handleGoodbyeMessage(session, deviceSession);
            } else if ("iot".equals(type)) {
                // 处理iot消息（设备状态更新）
                handleIotMessage(session, deviceSession, request);
            } else {
                sendErrorMessage(session, "不支持的消息类型: " + type);
            }
        } catch (Exception e) {
            System.err.println("处理文本消息出错: " + e.getMessage());
            e.printStackTrace();
            sendErrorMessage(session, "处理消息失败: " + e.getMessage());
        }
    }
    
    @Override
    protected void handleBinaryMessage(WebSocketSession session, BinaryMessage message) {
        // 处理二进制消息（音频数据）
        try {
            ByteBuffer buffer = message.getPayload();
            byte[] audioData = new byte[buffer.remaining()];
            buffer.get(audioData);
            
            // 如果数据太小（例如只有1字节），可能是心跳或测试数据，直接忽略，不打印日志
            if (audioData.length <= 1) {
                return;
            }
            
            // 获取设备会话
            String sessionId = session.getId();
            DeviceSession deviceSession = deviceSessions.get(sessionId);
            
            // 确保会话有效
            if (deviceSession == null) {
                System.err.println("设备会话无效: " + sessionId);
                return;
            }
            
            // 仅当设备处于监听状态且不在处理唤醒词时才处理音频数据和打印日志
            if (!deviceSession.isListening() || deviceSession.processingWakeWord) {
                // 设备未处于监听状态或正在处理唤醒词，不处理音频数据，也不打印日志
                return;
            }
            
            // 输出接收到的音频数据大小
            System.out.println("收到设备二进制消息: " + audioData.length + " 字节");
            
            // 使用线程池异步处理，避免阻塞WebSocket线程
            processAudioWithVad(session, audioData);
            
        } catch (Exception e) {
            System.err.println("处理二进制消息失败: " + e.getMessage());
            e.printStackTrace();
            try {
                sendErrorMessage(session, "处理音频失败: " + e.getMessage());
            } catch (Exception ex) {
                System.err.println("发送错误消息失败: " + ex.getMessage());
            }
        }
    }
    
    /**
     * 使用VAD处理音频数据
     */
    private void processAudioWithVad(WebSocketSession session, byte[] audioData) {
        // 使用线程池异步处理，避免阻塞WebSocket线程
        executorService.submit(() -> {
            try {
                String sessionId = session.getId();
                DeviceSession deviceSession = deviceSessions.get(sessionId);
                
                // 确保会话有效
                if (deviceSession == null) {
                    System.err.println("设备会话无效: " + sessionId);
                    return;
                }
                
                // 确保设备处于监听状态且不在处理唤醒词
                if (!deviceSession.isListening() || deviceSession.processingWakeWord) {
                    return;
                }
                
                // 检查长时间静音，如果超过5秒无语音，则断开连接
                if (vadService.isLongSilenceDetected(sessionId)) {
                    System.out.println("检测到5秒静音，准备断开连接: " + sessionId);
                    
                    // 生成并发送待机语音
                    String goodbyeText = "检测到长时间静音，我将进入待机状态";
                    processTextToSpeech(session, deviceSession, goodbyeText);
                    
                    // 发送待机消息
                    sendStatusMessage(session, "long_silence_detected", "检测到长时间静音，设备将进入待机状态");
                    
                    // 停止监听
                    deviceSession.stopListening();
                    
                    // 延迟一段时间后断开WebSocket连接
                    scheduledExecutorService.schedule(() -> {
                        try {
                            System.out.println("检测到长时间静音，主动断开WebSocket连接: " + sessionId);
                            session.close(CloseStatus.NORMAL);
                        } catch (IOException e) {
                            System.err.println("关闭WebSocket连接失败: " + e.getMessage());
                            // 强制关闭连接
                            try {
                                session.close(CloseStatus.SESSION_NOT_RELIABLE);
                            } catch (IOException ex) {
                                System.err.println("强制关闭连接失败: " + ex.getMessage());
                            }
                        }
                    }, 1000, TimeUnit.MILLISECONDS); // 延迟1秒后关闭连接
                    
                    return;
                }

                // 处理VAD
                VadService.VadResult result = vadService.processAudio(sessionId, audioData);
                
                // 根据VAD结果处理
                if (result.getStatus() == VadService.VadStatus.SPEECH_START) {
                    // 语音开始，开始收集音频
                    deviceSession.audioDataBuffer.reset();
                    if (result.getProcessedData() != null) {
                        deviceSession.addAudioData(result.getProcessedData());
                    }
                    
                    System.out.println("VAD: 语音开始, SessionId: " + sessionId);
                    deviceSession.speechStartTime = System.currentTimeMillis();
                    
                } else if (result.getStatus() == VadService.VadStatus.SPEECH_CONTINUE) {
                    // 语音继续，继续收集音频
                    if (result.getProcessedData() != null) {
                        deviceSession.addAudioData(result.getProcessedData());
                    }
                    
                } else if (result.getStatus() == VadService.VadStatus.SPEECH_END) {
                    // 语音结束，进行处理
                    System.out.println("VAD: 语音结束, SessionId: " + sessionId);
                    if (result.getProcessedData() != null) {
                        deviceSession.addAudioData(result.getProcessedData());
                    }
                    
                    // 处理收集到的语音数据
                    handleVoiceInput(session, deviceSession);
                    
                } else if (result.getStatus() == VadService.VadStatus.ERROR) {
                    System.err.println("VAD错误: " + sessionId);
                }
                
            } catch (Exception e) {
                System.err.println("处理音频失败: " + e.getMessage());
                e.printStackTrace();
            }
        });
    }
    
    /**
     * 强制检查并断开所有长时间静音的连接
     */
    public void forceDisconnectInactiveSessions() {
        System.out.println("强制检查并断开长时间静音会话...");
        
        for (Map.Entry<String, WebSocketSession> entry : sessions.entrySet()) {
            String sessionId = entry.getKey();
            WebSocketSession session = entry.getValue();
            
            if (session.isOpen() && vadService.isSessionInitialized(sessionId)) {
                DeviceSession deviceSession = deviceSessions.get(sessionId);
                
                if (deviceSession != null && deviceSession.isListening()) {
                    try {
                        System.out.println("强制断开会话: " + sessionId);
                        session.close(CloseStatus.NORMAL);
                    } catch (IOException e) {
                        System.err.println("强制关闭连接失败: " + e.getMessage());
                    }
                }
            }
        }
    }
    
    /**
     * 处理hello消息（设备初始化连接）
     */
    private void handleHelloMessage(WebSocketSession session, DeviceSession deviceSession, Map<String, Object> request) throws IOException {
        System.out.println("处理hello消息: " + request);
        
        // 提取版本信息
        Integer version = request.containsKey("version") ? (Integer) request.get("version") : 1;
        deviceSession.setVersion(version);
        
        // 获取音频参数
        @SuppressWarnings("unchecked")
        Map<String, Object> audioParams = (Map<String, Object>) request.get("audio_params");
        if (audioParams != null) {
            String format = (String) audioParams.get("format");
            Integer sampleRate = (Integer) audioParams.get("sample_rate");
            Integer channels = (Integer) audioParams.get("channels");
            Integer frameDuration = (Integer) audioParams.get("frame_duration");
            
            deviceSession.setAudioParams(format, sampleRate, channels, frameDuration);
            
            // 记录客户端请求的参数
            System.out.println("客户端音频参数: format=" + format + 
                              ", sample_rate=" + sampleRate + 
                              ", channels=" + channels + 
                              ", frame_duration=" + frameDuration);
            
            // 更新AudioCodec帧时长
            if (frameDuration != null && frameDuration > 0) {
                audioCodec.updateFrameDuration(frameDuration);
            }
        }
        
        // 获取功能特性
        @SuppressWarnings("unchecked")
        Map<String, Object> features = (Map<String, Object>) request.get("features");
        if (features != null) {
            Boolean aec = (Boolean) features.get("aec");
            Boolean mcp = (Boolean) features.get("mcp");
            
            deviceSession.setFeatures(aec, mcp);
        }
        
        // 生成会话ID
        String uniqueSessionId = UUID.randomUUID().toString();
        deviceSession.setUniqueSessionId(uniqueSessionId);
        
        // 构建hello响应
        Map<String, Object> response = new HashMap<>();
        response.put("type", "hello");
        response.put("session_id", uniqueSessionId);
        response.put("version", version); // 使用客户端发来的版本号
        response.put("transport", "websocket"); // 添加transport字段，客户端会检查此字段
        
        // 音频参数
        Map<String, Object> respAudioParams = new HashMap<>();
        respAudioParams.put("format", "opus");
        respAudioParams.put("sample_rate", 16000);
        respAudioParams.put("channels", 1);
        // 使用客户端发来的frame_duration
        Integer frameDuration = 20;
        if (audioParams != null && audioParams.get("frame_duration") != null) {
            frameDuration = (Integer) audioParams.get("frame_duration");
        }
        respAudioParams.put("frame_duration", frameDuration);
        response.put("audio_params", respAudioParams);
        
        // 发送响应
        String jsonResponse = objectMapper.writeValueAsString(response);
        session.sendMessage(new TextMessage(jsonResponse));
        
        System.out.println("已发送hello响应: " + jsonResponse);
    }
    
    /**
     * 处理listen消息（开始/停止监听）
     */
    private void handleListenMessage(WebSocketSession session, DeviceSession deviceSession, Map<String, Object> request) throws IOException {
        String state = (String) request.get("state");
        String mode = (String) request.get("mode");
        
        if ("start".equals(state)) {
            // 如果设备正在处理唤醒词，忽略开始监听请求
            if (deviceSession.processingWakeWord) {
                System.out.println("设备正在处理唤醒词，忽略开始监听请求");
                return;
            }
            // 重置VAD会话状态，确保干净的开始
            vadService.resetSession(session.getId());
            // 初始化VAD会话
            vadService.initSession(session.getId());
            // 开始监听
            deviceSession.startListening(mode != null ? mode : "auto");
            System.out.println("设备开始监听，模式: " + mode);
        } else if ("stop".equals(state)) {
            // 停止监听，清理资源
            deviceSession.stopListening();
            System.out.println("设备停止监听");
            // 重置VAD会话
            vadService.resetSession(session.getId());
        } else if ("detect".equals(state)) {
            // 处理唤醒词检测消息
            String text = (String) request.get("text");
            System.out.println("检测到唤醒词: " + text);
            
            // 停止当前可能的监听状态
            deviceSession.stopListening();
            
            // 设置标记，表示正在处理唤醒词
            deviceSession.processingWakeWord = true;
            
            // 直接处理唤醒词文本，不进入监听状态
            processWakeWord(session, deviceSession, text);
        } else {
            System.out.println("未知的监听状态: " + state);
        }
    }
    
    /**
     * 处理AI回复文本，生成TTS语音，并发送给设备
     */
    private void processWakeWord(WebSocketSession session, DeviceSession deviceSession, String wakeWord) {
        try {
            // 标记设备为正在处理唤醒词状态，防止进入监听状态
            deviceSession.processingWakeWord = true;
            
            // 处理唤醒词并获取AI回复
            String aiResponse = vllmService.process(wakeWord);
            
            if (aiResponse != null && !aiResponse.isEmpty()) {
                System.out.println("AI回复: " + aiResponse);
                
                // 构造回复消息
                Map<String, Object> response = new HashMap<>();
                response.put("type", "response");
                response.put("text", aiResponse);
                
                // 发送文本回复
                String jsonResponse = objectMapper.writeValueAsString(response);
                session.sendMessage(new TextMessage(jsonResponse));
                
                // 使用新的流式TTS处理方法
                processTextToSpeech(session, deviceSession, aiResponse);
            } else {
                System.err.println("AI未返回有效回复");
                // 如果没有AI回复，重置标记
                deviceSession.processingWakeWord = false;
                
                // 发送错误消息
                Map<String, Object> errorResponse = new HashMap<>();
                errorResponse.put("type", "error");
                errorResponse.put("message", "AI未能生成回复");
                String jsonErrorResponse = objectMapper.writeValueAsString(errorResponse);
                session.sendMessage(new TextMessage(jsonErrorResponse));
            }
        } catch (Exception e) {
            System.err.println("处理唤醒词失败: " + e.getMessage());
            e.printStackTrace();
            
            // 确保出错时也重置标记
            deviceSession.processingWakeWord = false;
            
            // 尝试发送错误消息
            try {
                Map<String, Object> errorResponse = new HashMap<>();
                errorResponse.put("type", "error");
                errorResponse.put("message", "处理唤醒词失败: " + e.getMessage());
                String jsonErrorResponse = objectMapper.writeValueAsString(errorResponse);
                session.sendMessage(new TextMessage(jsonErrorResponse));
            } catch (Exception ex) {
                System.err.println("发送错误消息失败: " + ex.getMessage());
            }
        }
    }
    
    /**
     * 处理goodbye消息（关闭会话）
     */
    private void handleGoodbyeMessage(WebSocketSession session, DeviceSession deviceSession) throws IOException {
        // 清理资源
        deviceSession.cleanup();
        
        // 构建响应
        Map<String, Object> response = new HashMap<>();
        response.put("type", "goodbye");
        response.put("session_id", deviceSession.getUniqueSessionId());
        
        // 发送响应
        String jsonResponse = objectMapper.writeValueAsString(response);
        session.sendMessage(new TextMessage(jsonResponse));
        
        System.out.println("已发送goodbye响应");
    }
    
    /**
     * 发送音频响应
     */
    private void sendAudioResponse(WebSocketSession session, DeviceSession deviceSession, byte[] audioData) throws IOException {
        if (session != null && session.isOpen()) {
            synchronized (session) {
                // 分片发送大数据包，每片最大4KB
                int maxChunkSize = 4 * 1024; // 4KB
                int totalChunks = (int) Math.ceil((double) audioData.length / maxChunkSize);
                
                System.out.println("开始分片发送PCM数据，共" + totalChunks + "片");
                
                try {
                    for (int i = 0; i < totalChunks; i++) {
                        int start = i * maxChunkSize;
                        int end = Math.min(start + maxChunkSize, audioData.length);
                        int chunkSize = end - start;
                        
                        // 创建数据片段
                        byte[] chunk = new byte[chunkSize];
                        System.arraycopy(audioData, start, chunk, 0, chunkSize);
                        
                        // 发送分片
                        session.sendMessage(new BinaryMessage(chunk));
                        
                        // 短暂延迟，避免缓冲区溢出
                        Thread.sleep(20);
                        
                        System.out.println("已发送第" + (i+1) + "/" + totalChunks + "片PCM数据，大小: " + chunkSize + " 字节");
                    }
                    
                    System.out.println("PCM数据分片发送完成");
                } catch (InterruptedException e) {
                    System.err.println("发送音频数据被中断: " + e.getMessage());
                    Thread.currentThread().interrupt();
                }
            }
        } else {
            System.err.println("无法发送音频响应：WebSocket会话已关闭或为null");
        }
        
        // 新增：保存发送给ESP32的音频数据到本地文件
        try {
            java.nio.file.Path savePath = java.nio.file.Paths.get("D:/ESP32/Springboot/last_sent_audio.pcm");
            java.nio.file.Files.createDirectories(savePath.getParent());
            java.nio.file.Files.write(savePath, audioData);
            System.out.println("[DEBUG] 已保存发送给ESP32的音频文件: D:/ESP32/Springboot/last_sent_audio.pcm");
        } catch (Exception e) {
            System.err.println("[DEBUG] 保存音频文件失败: " + e.getMessage());
        }
    }
    
    /**
     * 发送错误消息
     */
    private void sendErrorMessage(WebSocketSession session, String errorMessage) throws IOException {
        if (session != null && session.isOpen()) {
            synchronized (session) {
                Map<String, String> response = new HashMap<>();
                response.put("type", "error");
                response.put("message", errorMessage);
                
                String jsonResponse = objectMapper.writeValueAsString(response);
                session.sendMessage(new TextMessage(jsonResponse));
                
                System.err.println("已发送错误消息: " + errorMessage);
            }
        } else {
            System.err.println("无法发送错误消息：WebSocket会话已关闭或为null");
        }
    }
    
    /**
     * 在服务关闭时释放资源
     */
    @PreDestroy
    public void destroy() {
        try {
            // 关闭所有WebSocket会话
            for (WebSocketSession session : sessions.values()) {
                try {
                    if (session.isOpen()) {
                        session.close(CloseStatus.GOING_AWAY);
                    }
                } catch (IOException e) {
                    System.err.println("关闭WebSocket会话失败: " + e.getMessage());
                }
            }
            
            // 清理资源
            sessions.clear();
            deviceSessions.clear();
            
            // 关闭线程池
            executorService.shutdown();
            scheduledExecutorService.shutdown();
            try {
                if (!executorService.awaitTermination(5, TimeUnit.SECONDS)) {
                    executorService.shutdownNow();
                }
                if (!scheduledExecutorService.awaitTermination(5, TimeUnit.SECONDS)) {
                    scheduledExecutorService.shutdownNow();
                }
            } catch (InterruptedException e) {
                executorService.shutdownNow();
                scheduledExecutorService.shutdownNow();
                Thread.currentThread().interrupt();
            }
            
            System.out.println("WebSocket处理器已关闭");
        } catch (Exception e) {
            System.err.println("关闭WebSocket处理器失败: " + e.getMessage());
        }
    }
    
    /**
     * 设备会话类，存储每个设备连接的状态和信息
     */
    private class DeviceSession {
        private final String id;
        private String uniqueSessionId;
        private int version = 1;
        private boolean listening = false;
        private String listeningMode = "auto";
        private int sequenceNumber = 0;
        
        // 音频参数
        private String audioFormat = "opus";
        private int sampleRate = 16000;
        private int channels = 1;
        private int frameDuration = 20;
        
        // 特性标志
        private boolean aecEnabled = false;
        private boolean mcpEnabled = false;
        
        private int volume = 70; // 默认音量为70%
        private int brightness = 100; // 默认亮度为100%
        
        // TTS会话管理参数
        private String tts_first_text = null;
        private String tts_last_text = null;
        private double tts_duration = 0.0;
        private double tts_start_speak_time = 0.0;
        private boolean llm_finish_task = false;
        
        // 音频数据缓存
        private final ByteArrayOutputStream audioDataBuffer = new ByteArrayOutputStream();
        
        // VAD相关参数
        private long speechStartTime = 0;
        
        // 新增字段
        private boolean processingWakeWord = false;
        
        public DeviceSession(String id) {
            this.id = id;
        }
        
        public String getId() {
            return id;
        }
        
        public String getUniqueSessionId() {
            return uniqueSessionId;
        }
        
        public void setUniqueSessionId(String uniqueSessionId) {
            this.uniqueSessionId = uniqueSessionId;
        }
        
        public int getVersion() {
            return version;
        }
        
        public void setVersion(int version) {
            this.version = version;
        }
        
        public boolean isListening() {
            return listening;
        }
        
        public void startListening(String mode) {
            this.listening = true;
            this.listeningMode = mode;
        }
        
        public void stopListening() {
            this.listening = false;
        }
        
        public int getNextSequence() {
            return ++sequenceNumber;
        }
        
        public void setAudioParams(String format, int sampleRate, int channels, int frameDuration) {
            if (format != null) this.audioFormat = format;
            if (sampleRate > 0) this.sampleRate = sampleRate;
            if (channels > 0) this.channels = channels;
            if (frameDuration > 0) this.frameDuration = frameDuration;
        }
        
        public void setFeatures(Boolean aec, Boolean mcp) {
            if (aec != null) this.aecEnabled = aec;
            if (mcp != null) this.mcpEnabled = mcp;
        }
        
        public void cleanup() {
            // 清理资源
            listening = false;
            audioDataBuffer.reset();
            
            // 重置TTS会话参数
            tts_first_text = null;
            tts_last_text = null;
            tts_duration = 0.0;
            tts_start_speak_time = 0.0;
            llm_finish_task = false;
            
            // VAD相关参数
            speechStartTime = 0;
        }
        
        /**
         * 添加音频数据到缓冲区
         */
        public void addAudioData(byte[] data) {
            if (data == null || data.length == 0) {
                return;
            }
            
            try {
                audioDataBuffer.write(data);
            } catch (IOException e) {
                System.err.println("添加音频数据失败: " + e.getMessage());
            }
        }
        
        /**
         * 获取并清除缓冲区中的所有音频数据
         */
        public byte[] getAndClearAudioData() throws IOException {
            byte[] data = audioDataBuffer.toByteArray();
            audioDataBuffer.reset();
            return data;
        }
        
        public int getVolume() {
            return volume;
        }
        
        public void setVolume(int volume) {
            if (volume >= 0 && volume <= 100) {
                this.volume = volume;
            }
        }
        
        public int getBrightness() {
            return brightness;
        }
        
        public void setBrightness(int brightness) {
            if (brightness >= 0 && brightness <= 100) {
                this.brightness = brightness;
            }
        }
    }

    /**
     * 发送TTS状态消息
     */
    private void sendTtsMessage(WebSocketSession session, String state, String text, String sessionId) {
        try {
            Map<String, Object> message = new HashMap<>();
            message.put("type", "tts");
            message.put("state", state);  // 可能的状态：start, sentence_start, stop
            message.put("session_id", sessionId);
            
            if (text != null) {
                message.put("text", text);
            }
            
            String jsonMessage = objectMapper.writeValueAsString(message);
            session.sendMessage(new TextMessage(jsonMessage));
            
            System.out.println("发送TTS状态消息: " + state + (text != null ? ", 文本: " + text : ""));
        } catch (IOException e) {
            System.err.println("发送TTS状态消息失败: " + e.getMessage());
            e.printStackTrace();
        }
    }
    
    /**
     * 处理文本到语音转换，使用分段处理和流式传输
     */
    private void processTextToSpeech(WebSocketSession session, DeviceSession deviceSession, String text) {
        if (text == null || text.trim().isEmpty()) {
            System.err.println("TTS文本为空，跳过处理");
            return;
        }
        
        // 标记设备为正在处理TTS状态
        deviceSession.processingWakeWord = true;
        
        try {
            // 发送TTS开始消息
            sendTtsMessage(session, "start", null, deviceSession.getUniqueSessionId());
            
            // 分段处理长文本
            List<String> segments = splitTextIntoSegments(text);
            int totalSegments = segments.size();
            
            System.out.println("文本被分为 " + totalSegments + " 段");
            for (int i = 0; i < segments.size(); i++) {
                System.out.println("段落 " + (i+1) + ": " + segments.get(i) + " [长度: " + segments.get(i).length() + "]");
            }
            
            // 使用队列存储处理好的段落
            ConcurrentLinkedQueue<SegmentData> segmentQueue = new ConcurrentLinkedQueue<>();
            Object notifyLock = new Object();
            
            // 统计计数器
            AtomicInteger totalFrameCounter = new AtomicInteger(0);
            AtomicInteger totalBytesCounter = new AtomicInteger(0);
            AtomicInteger processedSegments = new AtomicInteger(0);
            
            // 创建发送线程
            Thread sendThread = new Thread(() -> {
                try {
                    long startTime = System.currentTimeMillis();
                    int nextIndexToSend = 0; // 下一个要发送的段落索引
                    
                    while (processedSegments.get() < totalSegments || !segmentQueue.isEmpty()) {
                        SegmentData segment = null;
                        
                        // 尝试从队列中获取已处理的段落
                        synchronized (notifyLock) {
                            // 查找下一个要发送的段落
                            Iterator<SegmentData> iterator = segmentQueue.iterator();
                            while (iterator.hasNext()) {
                                SegmentData s = iterator.next();
                                if (s.index == nextIndexToSend) {
                                    segment = s;
                                    iterator.remove();
                                    break;
                                }
                            }
                            
                            // 如果没有找到当前索引的段落，且还有段落未处理完成，则等待
                            if (segment == null) {
                                // 如果所有段落都已处理完毕且队列中没有下一个要发送的段落，则退出循环
                                if (processedSegments.get() >= totalSegments) {
                                    // 再次检查队列中是否还有未发送的段落
                                    boolean hasMoreSegments = false;
                                    for (SegmentData s : segmentQueue) {
                                        if (s.index >= nextIndexToSend) {
                                            hasMoreSegments = true;
                                            break;
                                        }
                                    }
                                    
                                    if (!hasMoreSegments) {
                                        break; // 所有段落都已处理且发送完毕
                                    }
                                }
                                
                                // 等待新段落可用
                                try {
                                    notifyLock.wait(500); // 最多等待500毫秒
                                } catch (InterruptedException e) {
                                    Thread.currentThread().interrupt();
                                    System.err.println("发送线程等待被中断: " + e.getMessage());
                                    return;
                                }
                                
                                // 检查是否被中断
                                if (Thread.currentThread().isInterrupted()) {
                                    System.out.println("发送线程被中断，退出循环");
                                    return;
                                }
                                
                                continue; // 继续下一次循环，尝试找到要发送的段落
                            }
                        }
                        
                        // 发送段落
                        if (segment != null) {
                            sendSegmentToClient(session, deviceSession.getUniqueSessionId(), segment, 
                                              deviceSession.frameDuration, totalFrameCounter, totalBytesCounter);
                            
                            // 更新下一个要发送的段落索引
                            nextIndexToSend++;
                        }
                    }
                    
                    // 计算总体统计信息
                    long endTime = System.currentTimeMillis();
                    long totalTime = endTime - startTime;
                    
                    // 计算每秒帧数和字节数
                    float framesPerSecond = totalFrameCounter.get() / (totalTime / 1000.0f);
                    float bytesPerSecond = totalBytesCounter.get() / (totalTime / 1000.0f);
                    
                    System.out.println(String.format("音频传输完成: 总共发送 %d 帧, %d 字节, 总耗时 %dms, %.2f 帧/秒, %.2f 字节/秒", 
                            totalFrameCounter.get(), totalBytesCounter.get(), totalTime, framesPerSecond, bytesPerSecond));
                    
                    // 发送TTS结束消息
                    sendTtsMessage(session, "stop", null, deviceSession.getUniqueSessionId());
                    
                    // 标记TTS处理完成
                    deviceSession.processingWakeWord = false;
                } catch (Exception e) {
                    System.err.println("发送线程异常: " + e.getMessage());
                    e.printStackTrace();
                    
                    try {
                        // 确保在出错时也发送停止消息
                        sendTtsMessage(session, "error", e.getMessage(), deviceSession.getUniqueSessionId());
                        deviceSession.processingWakeWord = false;
                    } catch (Exception ex) {
                        System.err.println("发送错误消息失败: " + ex.getMessage());
                    }
                }
            });
            
            // 启动发送线程 - 在处理线程启动后立即启动
            sendThread.setName("TTS-Send-Thread");
            sendThread.start();
            
            // 顺序处理段落，确保按顺序添加到队列
            for (int i = 0; i < segments.size(); i++) {
                final int index = i;
                final String segmentText = segments.get(i);
                
                try {
                    // 处理段落并加入队列
                    processSegmentAndQueue(segmentText, "中文女", deviceSession.frameDuration,
                                         index, totalSegments, segmentQueue, notifyLock);
                    
                    // 增加已处理段落计数
                    processedSegments.incrementAndGet();
                    
                    // 通知发送线程
                    synchronized (notifyLock) {
                        notifyLock.notify();
                    }
                } catch (Exception e) {
                    System.err.println("处理段落 " + (index+1) + "/" + totalSegments + " 失败: " + e.getMessage());
                    e.printStackTrace();
                    
                    // 即使失败也要增加计数，确保发送线程能够结束
                    processedSegments.incrementAndGet();
                    
                    // 通知发送线程
                    synchronized (notifyLock) {
                        notifyLock.notify();
                    }
                }
            }
            
            // 等待发送线程完成
            try {
                sendThread.join();
            } catch (InterruptedException e) {
                Thread.currentThread().interrupt();
                System.err.println("等待发送线程完成被中断: " + e.getMessage());
            }
            
        } catch (Exception e) {
            System.err.println("TTS处理失败: " + e.getMessage());
            e.printStackTrace();
            
            // 确保在出错时也重置标记
            deviceSession.processingWakeWord = false;
            
            // 发送TTS错误消息
            try {
                sendTtsMessage(session, "error", e.getMessage(), deviceSession.getUniqueSessionId());
            } catch (Exception ex) {
                System.err.println("发送TTS错误消息失败: " + ex.getMessage());
            }
        }
    }
    
    /**
     * 处理段落并加入队列
     */
    private void processSegmentAndQueue(String text, String speaker, int frameDuration, 
                                      int index, int total, 
                                      ConcurrentLinkedQueue<SegmentData> queue,
                                      Object notifyLock) {
        try {
            // 检查是否只包含表情符号，如果是则跳过处理
            if (text == null || text.trim().isEmpty() || containsOnlyEmoji(text)) {
                System.out.println("段落 " + (index + 1) + "/" + total + " 仅包含表情符号或为空，跳过处理");
                // 添加空的段落数据，保持顺序完整
                SegmentData emptySegment = new SegmentData(text, new ArrayList<>(), index);
                queue.add(emptySegment);
                synchronized (notifyLock) {
                    notifyLock.notify();
                }
                return;
            }
            
            long startTime = System.currentTimeMillis();
            System.out.println("开始处理段落 " + (index + 1) + "/" + total + ": " + text);
            
            // 发送TTS请求前记录日志
            System.out.println("发送TTS请求: " + cosyvoiceService.getCosyvoiceApiUrl() + " 段落: " + (index + 1) + "/" + total);
            
            // 生成当前段的语音 - 传入段落索引和总段数，确保按顺序处理
            byte[] speechData = cosyvoiceService.generateSpeech(text, speaker, frameDuration, index, total);
            
            if (speechData != null && speechData.length > 0) {
                // 提取Opus帧
                List<byte[]> opusFrames = extractOpusFrames(speechData, frameDuration);
                
                long processTime = System.currentTimeMillis() - startTime;
                System.out.println("段落 " + (index + 1) + "/" + total + " 生成成功，包含 " + 
                                  opusFrames.size() + " 帧，耗时 " + processTime + "ms");
                
                // 创建段落数据对象
                SegmentData segmentData = new SegmentData(text, opusFrames, index);
                
                // 添加到队列并通知发送线程
                queue.add(segmentData);
                synchronized (notifyLock) {
                    notifyLock.notify();
                }
                
                System.out.println("段落 " + (index + 1) + "/" + total + " 已添加到发送队列");
            } else {
                System.err.println("段落 " + (index + 1) + "/" + total + " 生成的语音数据为空");
                
                // 添加空的段落数据，保持顺序完整
                SegmentData emptySegment = new SegmentData(text, new ArrayList<>(), index);
                queue.add(emptySegment);
                synchronized (notifyLock) {
                    notifyLock.notify();
                }
            }
        } catch (Exception e) {
            System.err.println("处理段落 " + (index + 1) + "/" + total + " 失败: " + e.getMessage());
            e.printStackTrace();
            
            // 添加空的段落数据，保持顺序完整
            SegmentData emptySegment = new SegmentData(text, new ArrayList<>(), index);
            queue.add(emptySegment);
            synchronized (notifyLock) {
                notifyLock.notify();
            }
        }
    }
    
    /**
     * 发送段落到客户端
     */
    private void sendSegmentToClient(WebSocketSession session, String sessionId, 
                                   SegmentData segment, int frameDuration, 
                                   AtomicInteger totalFrameCounter, AtomicInteger totalBytesCounter) 
        throws Exception {
        
        if (segment == null || !session.isOpen()) {
            return;
        }
        
        System.out.println("发送段落 " + (segment.index + 1) + ": " + 
                          (segment.text.length() > 20 ? segment.text.substring(0, 17) + "..." : segment.text));
        
        // 如果这个段落只包含表情符号或为空，跳过发送音频部分
        if (containsOnlyEmoji(segment.text) || segment.text.trim().isEmpty()) {
            System.out.println("段落包含表情符号或为空，跳过音频发送");
            return;
        }
        
        // 发送句子开始标记
        sendTtsMessage(session, "sentence_start", segment.text, sessionId);
        
        if (segment.opusFrames == null || segment.opusFrames.isEmpty()) {
            System.out.println("段落没有音频帧可发送");
            return;
        }
        
        System.out.println("准备发送段落 " + (segment.index + 1) + "，包含 " + segment.opusFrames.size() + " 个Opus帧");
        
        // 参照参考服务器的实现，使用固定帧时长作为流控参数
        long startTime = System.currentTimeMillis();
        int playPosition = 0;
        
        // 预缓冲第一批帧以提高客户端响应速度
        int preCacheFrames = Math.min(3, segment.opusFrames.size());
        for (int i = 0; i < preCacheFrames; i++) {
            if (!session.isOpen()) break;
            session.sendMessage(new BinaryMessage(segment.opusFrames.get(i)));
            System.out.println("段落 " + (segment.index + 1) + " 预缓冲发送帧 " + (i + 1));
            
            // 统计字节数
            totalBytesCounter.addAndGet(segment.opusFrames.get(i).length);
        }
        
        // 发送剩余帧，带时间控制
        for (int i = preCacheFrames; i < segment.opusFrames.size(); i++) {
            if (!session.isOpen()) {
                System.err.println("WebSocket已关闭，停止发送");
                break;
            }
            
            // 计算预期发送时间
            long expectedTime = startTime + playPosition;
            long currentTime = System.currentTimeMillis();
            long delay = expectedTime - currentTime;
            
            if (delay > 0) {
                Thread.sleep(delay);
            }
            
            // 发送当前帧
            session.sendMessage(new BinaryMessage(segment.opusFrames.get(i)));
            
            if ((i + 1) % 10 == 0 || i == segment.opusFrames.size() - 1) {
                System.out.println("段落 " + (segment.index + 1) + " 已发送 " + (i + 1) + "/" + segment.opusFrames.size() + " 帧");
            }
            
            // 增加播放位置 (以毫秒为单位)
            playPosition += frameDuration;
            
            // 统计字节数
            totalBytesCounter.addAndGet(segment.opusFrames.get(i).length);
        }
        
        long segmentTime = System.currentTimeMillis() - startTime;
        System.out.println("段落 " + (segment.index + 1) + " 传输完成: 总共发送 " + segment.opusFrames.size() + " 帧，耗时 " + segmentTime + "ms");
        
        // 更新总帧数计数器
        totalFrameCounter.addAndGet(segment.opusFrames.size());
    }
    
    /**
     * 过滤表情符号
     */
    private String filterEmoji(String input) {
        if (input == null || input.isEmpty()) {
            return input;
        }
        
        // 如果输入字符串只包含表情符号，返回空字符串
        if (containsOnlyEmoji(input)) {
            System.out.println("文本仅包含表情符号，跳过TTS处理: " + input);
            return "";
        }
        
        try {
            // 使用正则表达式移除表情符号
            Pattern pattern = Pattern.compile(
                "[\\ud83c\\udc00-\\ud83c\\udfff]|[\\ud83d\\udc00-\\ud83d\\udfff]|[\\u2600-\\u27ff]",
                Pattern.UNICODE_CASE | Pattern.CASE_INSENSITIVE
            );
            Matcher matcher = pattern.matcher(input);
            String result = matcher.replaceAll("");
            
            // 如果过滤后字符串为空或只有空白，但原文本不是空白
            if (result.trim().isEmpty() && !input.trim().isEmpty()) {
                System.out.println("过滤表情符号后文本为空，返回默认文本");
                return "您发送了表情";
            }
            
            return result;
        } catch (Exception e) {
            System.err.println("过滤表情符号时发生错误: " + e.getMessage());
            e.printStackTrace();
            return input;  // 出错时返回原始输入
        }
    }

    /**
     * 检查字符串是否只包含表情符号和空白字符
     */
    private boolean containsOnlyEmoji(String input) {
        if (input == null || input.trim().isEmpty()) {
            return false;
        }
        
        for (char c : input.toCharArray()) {
            if (!Character.isWhitespace(c) && !isEmoji(c)) {
                return false;
            }
        }
        return true;
    }
    
    /**
     * 检查字符是否为表情符号
     */
    private boolean isEmoji(char c) {
        return (c >= '\uD83C' && c <= '\uD83E') || // SMP (Supplementary Multilingual Plane)
               (c >= '\u2600' && c <= '\u27BF');   // 其他常见表情符号范围
    }
    
    /**
     * 表示已处理完成的段落数据
     */
    private static class SegmentData {
        final String text;
        final List<byte[]> opusFrames;
        int index;
        
        SegmentData(String text, List<byte[]> opusFrames, int index) {
            this.text = text;
            this.opusFrames = opusFrames;
            this.index = index;
        }
    }
    
    /**
     * 将长文本按照标点符号分段
     * @param text 待分段文本
     * @return 分段列表
     */
    private List<String> splitTextIntoSegments(String text) {
        List<String> segments = new ArrayList<>();
        
        if (text == null || text.isEmpty()) {
            return segments;
        }
        
        System.out.println("开始文本分段，原文本长度: " + text.length());
        
        // 分段的最大、最小长度控制
        int maxSegmentLength = 30; // 每段最大字符数
        int minSegmentLength = 5; // 每段最小字符数
        
        // 更简单高效的分段逻辑
        StringBuilder currentSegment = new StringBuilder();
        int currentPos = 0;
        
        while (currentPos < text.length()) {
            char c = text.charAt(currentPos);
            currentSegment.append(c);
            currentPos++;
            
            // 检查是否是表情符号，如果是则跳过
            if (isEmoji(c)) {
                continue;
            }
            
            // 检查是否是标点符号并且已经达到最小长度
            boolean isPunctuation = "，。！？；：,.!?;:".indexOf(c) >= 0;
            
            if (isPunctuation && currentSegment.length() >= minSegmentLength) {
                // 句号、问号、感叹号、分号、冒号总是分段
                if ("。.！!？?；;：:".indexOf(c) >= 0) {
                    segments.add(currentSegment.toString());
                    currentSegment = new StringBuilder();
                }
                // 逗号只在达到足够长度时分段
                else if (("，,".indexOf(c) >= 0) && currentSegment.length() >= maxSegmentLength) {
                    segments.add(currentSegment.toString());
                    currentSegment = new StringBuilder();
                }
            }
            // 如果没有标点但段落已经足够长，强制分段
            else if (currentSegment.length() >= maxSegmentLength * 1.5) {
                segments.add(currentSegment.toString());
                currentSegment = new StringBuilder();
            }
        }
        
        // 添加最后一个分段（如果有）
        if (currentSegment.length() > 0) {
            segments.add(currentSegment.toString());
        }
        
        // 如果分段结果为空，则直接返回原文本作为一个段落
        if (segments.isEmpty()) {
            segments.add(text);
        }
        
        System.out.println("文本分段完成，共 " + segments.size() + " 段");
        return segments;
    }
    
    /**
     * 从opus音频数据中提取帧
     * 简化版：基于参考服务端，专门处理CosyVoice生成的Opus帧格式
     * 
     * @param opusData Opus格式的音频数据
     * @param frameDurationMs 帧时长(毫秒)
     * @return Opus帧列表
     */
    private List<byte[]> extractOpusFrames(byte[] opusData, int frameDurationMs) {
        List<byte[]> frames = new ArrayList<>();
        
        if (opusData == null || opusData.length == 0) {
            System.err.println("警告: 输入Opus数据为空");
            return frames;
        }
        
        // 查看数据开头来判断格式类型
        boolean isOgg = opusData.length > 4 && 
                        opusData[0] == 'O' && opusData[1] == 'g' && 
                        opusData[2] == 'g' && opusData[3] == 'S';
        
        // 1. 如果是OGG容器格式(不太可能，但作为健壮性处理)
        if (isOgg) {
            System.out.println("检测到OGG容器格式，尝试提取Opus帧");
            // 不进行复杂的OGG解析，简单跳过头部
            int skipHeaderBytes = 100;
            if (opusData.length <= skipHeaderBytes) {
                System.err.println("警告: OGG数据太小，无法提取帧");
                return frames;
            }
            
            // 为了简单，我们把整个数据作为一个大帧返回
            // 对于ESP32来说不理想，但至少能工作
            byte[] frame = new byte[opusData.length - skipHeaderBytes];
            System.arraycopy(opusData, skipHeaderBytes, frame, 0, frame.length);
            frames.add(frame);
            System.out.println("从OGG容器提取了1个Opus帧，大小: " + frame.length + "字节");
            
            return frames;
        }
        
        // 2. 尝试CosyVoice标准格式：2字节长度 + 帧数据
        if (tryParseCosyVoiceFrames(opusData, frames)) {
                return frames;
            }
            
        // 3. 尝试使用基于TOC字节的Opus帧检测
        // Opus帧始终以TOC字节开始，此字节包含有关帧的信息
        if (tryParseRawOpusFrames(opusData, frames)) {
            return frames;
        }
        
        // 4. 如果前面的尝试都失败了，尝试估算帧大小并分段
        // 参考参考服务端，平均每帧150-160字节
        int estimatedFrameSize = 160;
        int frameCount = opusData.length / estimatedFrameSize;
        
        if (frameCount == 0) {
            // 如果数据太小，作为一个帧返回
            frames.add(opusData);
            System.out.println("数据太小，作为单帧处理，大小: " + opusData.length + "字节");
            return frames;
        }
        
        System.out.println("使用估算帧大小: " + estimatedFrameSize + " 字节，帧数: " + frameCount);
        
            for (int i = 0; i < frameCount; i++) {
            int offset = i * estimatedFrameSize;
                int size = Math.min(estimatedFrameSize, opusData.length - offset);
                
            if (size <= 0) break;
            
                    byte[] frame = new byte[size];
                    System.arraycopy(opusData, offset, frame, 0, size);
                    frames.add(frame);
                }
        
        // 处理最后一个可能的不完整帧
        int lastOffset = frameCount * estimatedFrameSize;
        if (lastOffset < opusData.length) {
            int lastSize = opusData.length - lastOffset;
            if (lastSize > 0) {
                byte[] lastFrame = new byte[lastSize];
                System.arraycopy(opusData, lastOffset, lastFrame, 0, lastSize);
                frames.add(lastFrame);
            }
        }
        
                return frames;
            }
            
    /**
     * 尝试解析CosyVoice格式的帧（每帧前有2字节长度）
     */
    private boolean tryParseCosyVoiceFrames(byte[] opusData, List<byte[]> frames) {
        int position = 0;
        int validFrames = 0;
        
        try {
            // 尝试读取前10个帧，看是否符合格式
            for (int i = 0; i < 10 && position + 2 < opusData.length; i++) {
                int frameLength = ((opusData[position] & 0xFF) | ((opusData[position + 1] & 0xFF) << 8));
                
                // 合理的帧长度在20-500字节之间
                if (frameLength < 20 || frameLength > 500 || position + 2 + frameLength > opusData.length) {
                    return false;
                }
                
                position += 2 + frameLength;
                validFrames++;
            }
            
            // 如果成功解析了至少3个有效帧，认为这是正确的格式
            if (validFrames >= 3) {
                position = 0;
                System.out.println("检测到CosyVoice帧格式（带2字节长度头）");
                
                // 正式解析所有帧
                while (position + 2 < opusData.length) {
                    int frameLength = ((opusData[position] & 0xFF) | ((opusData[position + 1] & 0xFF) << 8));
                    
                    if (frameLength < 20 || frameLength > 500 || position + 2 + frameLength > opusData.length) {
                        break;
                    }
                    
                    byte[] frame = new byte[frameLength];
                    System.arraycopy(opusData, position + 2, frame, 0, frameLength);
                    frames.add(frame);
                    
                    position += 2 + frameLength;
                }
                
                System.out.println("成功提取 " + frames.size() + " 个CosyVoice帧");
                return true;
            }
        } catch (Exception e) {
            System.err.println("解析CosyVoice帧失败: " + e.getMessage());
        }
        
        return false;
    }
    
    /**
     * 尝试解析原始Opus帧（通过TOC字节和帧边界检测）
     */
    private boolean tryParseRawOpusFrames(byte[] opusData, List<byte[]> frames) {
        // 这个方法实现较复杂，需要了解Opus帧格式
        // 简化实现：尝试查找常见的帧边界模式
        
        // Opus帧通常以特定的TOC字节开始，可以通过统计分析找出可能的帧边界
        // 这里使用简化的算法
        
        try {
            // 寻找重复的模式作为可能的帧边界
            Map<Byte, Integer> tocCounts = new HashMap<>();
            for (int i = 0; i < Math.min(opusData.length, 1000); i++) {
                byte b = opusData[i];
                tocCounts.put(b, tocCounts.getOrDefault(b, 0) + 1);
            }
            
            // 找出最常见的几个字节
            List<Byte> commonBytes = new ArrayList<>();
            for (Map.Entry<Byte, Integer> entry : tocCounts.entrySet()) {
                if (entry.getValue() >= 3) {
                    commonBytes.add(entry.getKey());
                }
            }
            
            if (commonBytes.isEmpty()) {
                return false;
            }
            
            // 找出这些字节在数据中的位置，可能是帧边界
            List<Integer> potentialBoundaries = new ArrayList<>();
            for (int i = 0; i < opusData.length - 1; i++) {
                if (commonBytes.contains(opusData[i])) {
                    potentialBoundaries.add(i);
                }
            }
            
            // 分析这些位置之间的距离是否有规律
            if (potentialBoundaries.size() < 3) {
                return false;
            }
            
            // 计算相邻边界之间的平均距离
            int totalDistance = 0;
            for (int i = 1; i < potentialBoundaries.size(); i++) {
                totalDistance += potentialBoundaries.get(i) - potentialBoundaries.get(i - 1);
            }
            int avgDistance = totalDistance / (potentialBoundaries.size() - 1);
            
            // 如果平均距离在合理范围内，使用它来分割帧
            if (avgDistance >= 40 && avgDistance <= 300) {
                System.out.println("检测到可能的Opus帧边界，平均帧大小: " + avgDistance + " 字节");
                
                // 根据检测到的边界分割帧
                for (int i = 0; i < potentialBoundaries.size() - 1; i++) {
                    int start = potentialBoundaries.get(i);
                    int end = potentialBoundaries.get(i + 1);
                    int size = end - start;
                    
                    if (size > 20 && size < 500) {
                    byte[] frame = new byte[size];
                        System.arraycopy(opusData, start, frame, 0, size);
                    frames.add(frame);
                }
                }
                
                // 处理最后一个帧
                int lastStart = potentialBoundaries.get(potentialBoundaries.size() - 1);
                if (lastStart < opusData.length) {
                    int lastSize = opusData.length - lastStart;
                    if (lastSize > 20 && lastSize < 500) {
                        byte[] lastFrame = new byte[lastSize];
                        System.arraycopy(opusData, lastStart, lastFrame, 0, lastSize);
                        frames.add(lastFrame);
                    }
                }
                
                System.out.println("成功提取 " + frames.size() + " 个原始Opus帧");
                return frames.size() > 0;
            }
        } catch (Exception e) {
            System.err.println("解析原始Opus帧失败: " + e.getMessage());
        }
        
        return false;
    }

    /**
     * 处理iot消息（设备状态更新）
     */
    private void handleIotMessage(WebSocketSession session, DeviceSession deviceSession, Map<String, Object> request) {
        try {
            // 简化日志输出，避免大量冗余信息
            
            // 获取设备状态
            @SuppressWarnings("unchecked")
            List<Map<String, Object>> states = (List<Map<String, Object>>) request.get("states");
            if (states != null) {
                // 仅输出简要状态信息
                System.out.println("处理设备状态更新，共 " + states.size() + " 项");
                
                for (Map<String, Object> deviceState : states) {
                    String name = (String) deviceState.get("name");
                    @SuppressWarnings("unchecked")
                    Map<String, Object> state = (Map<String, Object>) deviceState.get("state");
                    
                    if ("Speaker".equals(name) && state.containsKey("volume")) {
                        // 处理音量更新
                        Integer volume = (Integer) state.get("volume");
                        System.out.println("设备音量更新: " + volume + "%");
                        deviceSession.setVolume(volume);
                    } else if ("Backlight".equals(name) && state.containsKey("brightness")) {
                        // 处理背光亮度更新
                        Integer brightness = (Integer) state.get("brightness");
                        System.out.println("设备背光亮度更新: " + brightness + "%");
                        deviceSession.setBrightness(brightness);
                    }
                }
            }
            
            // 处理设备描述符
            @SuppressWarnings("unchecked")
            List<Map<String, Object>> descriptors = (List<Map<String, Object>>) request.get("descriptors");
            if (descriptors != null) {
                System.out.println("设备描述符: " + descriptors.size() + " 项");
            }
        } catch (Exception e) {
            System.err.println("处理iot消息失败: " + e.getMessage());
            e.printStackTrace();
        }
    }

    /**
     * 添加静音裁剪方法
     */
    private byte[] trimSilenceFromPCM(byte[] audio, int sampleRate, int frameMs, int energyThreshold) {
        int frameSize = sampleRate * frameMs / 1000 * 2; // 16bit单声道
        int totalFrames = audio.length / frameSize;
        int startFrame = 0, endFrame = totalFrames - 1;
        // 找起点
        for (int i = 0; i < totalFrames; i++) {
            double energy = calcFrameEnergy(audio, i * frameSize, frameSize);
            if (energy > energyThreshold) {
                startFrame = i;
                break;
            }
        }
        // 找终点
        for (int i = totalFrames - 1; i >= 0; i--) {
            double energy = calcFrameEnergy(audio, i * frameSize, frameSize);
            if (energy > energyThreshold) {
                endFrame = i;
                break;
            }
        }
        int start = startFrame * frameSize;
        int end = Math.min((endFrame + 1) * frameSize, audio.length);
        if (start >= end) return new byte[0];
        return java.util.Arrays.copyOfRange(audio, start, end);
    }
    private double calcFrameEnergy(byte[] data, int offset, int frameSize) {
        double sum = 0;
        int count = 0;
        for (int i = 0; i + 1 < frameSize && offset + i + 1 < data.length; i += 2) {
            short sample = (short)(((data[offset + i + 1] & 0xFF) << 8) | (data[offset + i] & 0xFF));
            sum += Math.abs(sample);
            count++;
        }
        return count > 0 ? sum / count : 0;
    }

    private byte[] pcmToWav(byte[] pcmData, int sampleRate, int channels, int bitsPerSample) {
        int byteRate = sampleRate * channels * bitsPerSample / 8;
        int blockAlign = channels * bitsPerSample / 8;
        int dataSize = pcmData.length;
        int chunkSize = 36 + dataSize;

        ByteArrayOutputStream out = new ByteArrayOutputStream();
        try {
            // RIFF header
            out.write("RIFF".getBytes("US-ASCII"));
            out.write(new byte[] {
                (byte) (chunkSize & 0xff),
                (byte) ((chunkSize >> 8) & 0xff),
                (byte) ((chunkSize >> 16) & 0xff),
                (byte) ((chunkSize >> 24) & 0xff)
            });
            out.write("WAVE".getBytes("US-ASCII"));

            // fmt subchunk
            out.write("fmt ".getBytes("US-ASCII"));
            out.write(new byte[] {16, 0, 0, 0}); // Subchunk1Size (16 for PCM)
            out.write(new byte[] {1, 0}); // AudioFormat (1 for PCM)
            out.write(new byte[] {(byte) channels, 0}); // NumChannels
            out.write(new byte[] {
                (byte) (sampleRate & 0xff),
                (byte) ((sampleRate >> 8) & 0xff),
                (byte) ((sampleRate >> 16) & 0xff),
                (byte) ((sampleRate >> 24) & 0xff)
            }); // SampleRate
            out.write(new byte[] {
                (byte) (byteRate & 0xff),
                (byte) ((byteRate >> 8) & 0xff),
                (byte) ((byteRate >> 16) & 0xff),
                (byte) ((byteRate >> 24) & 0xff)
            }); // ByteRate
            out.write(new byte[] {
                (byte) (blockAlign & 0xff),
                (byte) ((blockAlign >> 8) & 0xff)
            }); // BlockAlign
            out.write(new byte[] {
                (byte) bitsPerSample,
                0
            }); // BitsPerSample

            // data subchunk
            out.write("data".getBytes("US-ASCII"));
            out.write(new byte[] {
                (byte) (dataSize & 0xff),
                (byte) ((dataSize >> 8) & 0xff),
                (byte) ((dataSize >> 16) & 0xff),
                (byte) ((dataSize >> 24) & 0xff)
            });
            out.write(pcmData);

            return out.toByteArray();
        } catch (IOException e) {
            System.err.println("PCM to WAV conversion failed: " + e.getMessage());
            return null;
        }
    }

    private void saveAsWav(byte[] pcmData, String filename) {
//        try {
//            if (pcmData == null || pcmData.length == 0) {
//                System.err.println("[VAD] 尝试保存空音频，跳过: " + filename);
//                return;
//            }
//            byte[] wavData = pcmToWav(pcmData, 16000, 1, 16);
//            java.nio.file.Files.write(java.nio.file.Paths.get(filename), wavData);
//            System.out.println("[VAD] 已保存标准WAV音频文件: " + filename + ", 大小: " + wavData.length);
//        } catch (Exception e) {
//            System.err.println("[VAD] 保存标准WAV音频文件失败: " + e.getMessage());
//        }
    }

    /**
     * 发送状态消息到客户端
     */
    private void sendStatusMessage(WebSocketSession session, String status, String message) {
        try {
            Map<String, Object> response = new HashMap<>();
            response.put("type", "status");
            response.put("status", status);
            response.put("message", message);
            
            String json = objectMapper.writeValueAsString(response);
            session.sendMessage(new TextMessage(json));
        } catch (Exception e) {
            System.err.println("发送状态消息失败: " + e.getMessage());
        }
    }

/**
 * 处理收集到的语音输入
 */
private void handleVoiceInput(WebSocketSession session, DeviceSession deviceSession) {
    try {
        String sessionId = session.getId();
        
        // 立即停止监听，防止处理过程中接收新的音频
        deviceSession.stopListening();
        
        // 获取并处理完整的音频
        byte[] combinedAudio = deviceSession.getAndClearAudioData();
        
        // 检查音频数据是否有效
        if (combinedAudio == null || combinedAudio.length == 0) {
            System.out.println("无有效音频数据，忽略处理");
            return;
        }
        
        // 检查语音开始时间是否已设置
        if (deviceSession.speechStartTime <= 0) {
            System.out.println("语音开始时间未设置，使用当前时间");
            deviceSession.speechStartTime = System.currentTimeMillis() - 1000; // 假设语音持续了1秒
        }
        
        // 计算语音持续时间
        long speechDuration = System.currentTimeMillis() - deviceSession.speechStartTime;
        System.out.println("语音持续时间: " + speechDuration + " ms");
        
        // 重置语音开始时间，避免累积错误
        deviceSession.speechStartTime = 0;
        
        // 保存最终的音频数据和会话ID，以便在线程中使用
        final byte[] finalAudio = combinedAudio;
        final String finalSessionId = sessionId;
        
        // 使用线程处理语音识别和后续流程
        executorService.submit(() -> {
            try {
                // 发送语音状态消息
                Map<String, Object> statusResponse = new HashMap<>();
                statusResponse.put("type", "listening_status");
                statusResponse.put("status", "processing"); // 更新状态为"处理中"
                String statusJson = objectMapper.writeValueAsString(statusResponse);
                synchronized (session) {
                    session.sendMessage(new TextMessage(statusJson));
                }
                
                // 调用语音识别服务
                String recognizedText = null;
                System.out.println("调用语音识别服务，音频长度: " + (finalAudio != null ? finalAudio.length : 0));
                
                try {
                    // 保存音频文件用于调试
                    if (finalAudio != null && finalAudio.length > 0) {
                        String filename = "vad_audio_" + System.currentTimeMillis() + "_" + finalSessionId + ".wav";
                        saveAsWav(finalAudio, filename);
                    }
                    
                    // 调用语音识别
                    recognizedText = sensevoiceService.recognizeSpeech(finalAudio, finalSessionId);
                } catch (Exception e) {
                    System.err.println("语音识别失败: " + e.getMessage());
                    e.printStackTrace();
                }
                
                System.out.println("语音识别结果: " + recognizedText);
                
                // 检查是否包含告别词
                boolean hasGoodbye = false;
                boolean hasNegation = false;
                String[] goodbyeWords = {"再见", "拜拜", "退出", "离开", "关闭", "睡觉", "休息", "关机", "停止"};
                String[] negations = {"不", "不要", "不是", "别", "no", "not", "don't", "do not"};
                int goodbyeIdx = -1;
                if (recognizedText != null) {
                    for (String bye : goodbyeWords) {
                        int idx = recognizedText.indexOf(bye);
                        if (idx >= 0) {
                            hasGoodbye = true;
                            goodbyeIdx = idx;
                            // 检查否定词是否在告别词前3个字符内
                            for (String neg : negations) {
                                int negIdx = recognizedText.indexOf(neg);
                                if (negIdx >= 0 && negIdx < idx && (idx - negIdx) <= 3) {
                                    hasNegation = true;
                                    break;
                                }
                            }
                            break;
                        }
                    }
                }
                // 检查是否包含告别词，且无否定词
                if (hasGoodbye && !hasNegation) {
                    // 标记检测到告别词
                    vadService.setGoodbyeDetected(finalSessionId, true);
                    // 发送告别消息
                    Map<String, Object> goodbyeMsg = new HashMap<>();
                    goodbyeMsg.put("type", "command");
                    goodbyeMsg.put("command", "goodbye");
                    goodbyeMsg.put("message", "已检测到告别词，设备将进入待机状态");
                    String goodbyeJson = objectMapper.writeValueAsString(goodbyeMsg);
                    synchronized (session) {
                        session.sendMessage(new TextMessage(goodbyeJson));
                    }
                    // 生成并发送告别语音
                    String goodbyeText = "好的，我现在进入待机状态，需要时再叫醒我";
                    processTextToSpeech(session, deviceSession, goodbyeText);
                    // 设置状态为空闲
                    Map<String, Object> idleResponse = new HashMap<>();
                    idleResponse.put("type", "listening_status");
                    idleResponse.put("status", "idle");
                    String idleJson = objectMapper.writeValueAsString(idleResponse);
                    synchronized (session) {
                        session.sendMessage(new TextMessage(idleJson));
                    }
                    // 延迟一段时间后断开WebSocket连接，确保告别语音播放完成
                    scheduledExecutorService.schedule(() -> {
                        try {
                            System.out.println("检测到告别词，主动断开WebSocket连接: " + finalSessionId);
                            session.close(CloseStatus.NORMAL);
                        } catch (IOException e) {
                            System.err.println("关闭WebSocket连接失败: " + e.getMessage());
                        }
                    }, 3000, TimeUnit.MILLISECONDS); // 延迟3秒后关闭连接
                    return;
                }
                
                // 检查识别结果是否有效
                if (recognizedText == null || recognizedText.isEmpty()) {
                    System.out.println("未识别到有效语音内容");
                    // 发送识别失败状态
                    Map<String, Object> idleResponse = new HashMap<>();
                    idleResponse.put("type", "listening_status");
                    idleResponse.put("status", "idle"); // 重置状态为"空闲"
                    String idleJson = objectMapper.writeValueAsString(idleResponse);
                    synchronized (session) {
                        session.sendMessage(new TextMessage(idleJson));
                    }
                    return;
                }
                
                // 使用同步块来保证WebSocket消息发送的线程安全
                synchronized (session) {
                    // 发送识别结果
                    Map<String, Object> sttResponse = new HashMap<>();
                    sttResponse.put("type", "stt");
                    sttResponse.put("text", recognizedText);
                    String sttJson = objectMapper.writeValueAsString(sttResponse);
                    session.sendMessage(new TextMessage(sttJson));
                    
                    // 调用大语言模型处理文本
                    String aiResponse = vllmService.process(recognizedText);
                    
                    // 发送文本响应
                    Map<String, Object> textResponse = new HashMap<>();
                    textResponse.put("type", "text_response");
                    textResponse.put("text", recognizedText);
                    textResponse.put("response", aiResponse);
                    String textJson = objectMapper.writeValueAsString(textResponse);
                    session.sendMessage(new TextMessage(textJson));
                    
                    // 获取可用的语音合成音色
                    String[] speakers = cosyvoiceService.getSpeakers();
                    String speaker = speakers.length > 0 ? speakers[0] : "中文女";
                    
                    // 发送TTS开始消息
                    Map<String, Object> ttsResponse = new HashMap<>();
                    ttsResponse.put("type", "tts");
                    ttsResponse.put("state", "start");
                    String ttsJson = objectMapper.writeValueAsString(ttsResponse);
                    session.sendMessage(new TextMessage(ttsJson));
                    
                    // 生成语音并发送
                    System.out.println("生成语音，文本: " + aiResponse + ", 音色: " + speaker);
                    processTextToSpeech(session, deviceSession, aiResponse);
                }
                
            } catch (Exception e) {
                System.err.println("处理语音识别结果失败: " + e.getMessage());
                e.printStackTrace();
            }
        });
    } catch (Exception e) {
        System.err.println("处理语音输入失败: " + e.getMessage());
        e.printStackTrace();
    }
} 
}