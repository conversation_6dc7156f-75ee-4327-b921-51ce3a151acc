package com.example.service;

import java.io.File;
import java.lang.reflect.Field;
import java.util.Arrays;

/**
 * 用于动态设置本地库路径
 */
public class LibraryPathLoader {
    
    /**
     * 将指定目录添加到Java库搜索路径
     * 
     * @param directoryPath 要添加的目录路径
     * @return 是否成功添加
     */
    public static boolean addLibraryPath(String directoryPath) {
        try {
            File directory = new File(directoryPath);
            if (!directory.exists() || !directory.isDirectory()) {
                System.err.println("指定路径不存在或不是目录: " + directoryPath);
                return false;
            }
            
            System.out.println("添加本地库路径: " + directoryPath);
            
            // 获取当前的库搜索路径
            String currentPaths = System.getProperty("java.library.path");
            
            // 检查路径是否已存在
            if (currentPaths != null && Arrays.asList(currentPaths.split(File.pathSeparator)).contains(directoryPath)) {
                System.out.println("路径已存在于java.library.path中: " + directoryPath);
                return true;
            }
            
            // 添加新路径
            String newPaths = currentPaths == null ? directoryPath : currentPaths + File.pathSeparator + directoryPath;
            System.setProperty("java.library.path", newPaths);
            
            // 清除系统缓存
            Field sysPathsField = ClassLoader.class.getDeclaredField("sys_paths");
            sysPathsField.setAccessible(true);
            sysPathsField.set(null, null);
            
            System.out.println("成功添加库路径: " + directoryPath);
            return true;
        } catch (Exception e) {
            System.err.println("添加库路径失败: " + e.getMessage());
            e.printStackTrace();
            return false;
        }
    }
    
    /**
     * 查找并加载opus.dll库
     * 
     * @return 是否成功加载
     */
    public static boolean loadOpusLibrary() {
        try {
            // 获取当前工作目录
            String currentDir = System.getProperty("user.dir");
            System.out.println("当前工作目录: " + currentDir);
            
            // 检查当前目录
            File opusDll = new File(currentDir, "opus.dll");
            if (opusDll.exists()) {
                System.out.println("在当前目录找到opus.dll: " + opusDll.getAbsolutePath());
                return addLibraryPath(currentDir);
            }
            
            // 检查上级目录
            File parentDir = new File(currentDir).getParentFile();
            if (parentDir != null) {
                opusDll = new File(parentDir, "opus.dll");
                if (opusDll.exists()) {
                    System.out.println("在上级目录找到opus.dll: " + opusDll.getAbsolutePath());
                    return addLibraryPath(parentDir.getAbsolutePath());
                }
                
                // 检查opus_download目录
                File opusDownloadDir = new File(parentDir, "opus_download");
                opusDll = new File(opusDownloadDir, "opus.dll");
                if (opusDll.exists()) {
                    System.out.println("在opus_download目录找到opus.dll: " + opusDll.getAbsolutePath());
                    return addLibraryPath(opusDownloadDir.getAbsolutePath());
                }
            }
            
            System.err.println("未找到opus.dll文件，请确保它存在于项目目录中");
            return false;
        } catch (Exception e) {
            System.err.println("加载opus库失败: " + e.getMessage());
            e.printStackTrace();
            return false;
        }
    }
} 