<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>小智唤醒词配置管理</title>
    <style>
        body {
            font-family: 'Microsoft YaHei', Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            background: white;
            padding: 30px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        h1 {
            color: #333;
            text-align: center;
            margin-bottom: 30px;
        }
        .section {
            margin-bottom: 30px;
            padding: 20px;
            border: 1px solid #ddd;
            border-radius: 5px;
            background-color: #fafafa;
        }
        .section h3 {
            margin-top: 0;
            color: #555;
        }
        .form-group {
            margin-bottom: 15px;
        }
        label {
            display: block;
            margin-bottom: 5px;
            font-weight: bold;
            color: #333;
        }
        input[type="text"], select {
            width: 100%;
            padding: 10px;
            border: 1px solid #ddd;
            border-radius: 4px;
            font-size: 14px;
            box-sizing: border-box;
        }
        button {
            background-color: #007bff;
            color: white;
            padding: 10px 20px;
            border: none;
            border-radius: 4px;
            cursor: pointer;
            font-size: 14px;
            margin-right: 10px;
        }
        button:hover {
            background-color: #0056b3;
        }
        button.secondary {
            background-color: #6c757d;
        }
        button.secondary:hover {
            background-color: #545b62;
        }
        .status {
            padding: 10px;
            border-radius: 4px;
            margin-top: 10px;
        }
        .status.success {
            background-color: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
        .status.error {
            background-color: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }
        .status.info {
            background-color: #d1ecf1;
            color: #0c5460;
            border: 1px solid #bee5eb;
        }
        .current-config {
            background-color: #e7f3ff;
            padding: 15px;
            border-radius: 5px;
            margin-bottom: 20px;
        }
        .supported-words {
            display: flex;
            flex-wrap: wrap;
            gap: 10px;
            margin-top: 10px;
        }
        .word-tag {
            background-color: #e9ecef;
            padding: 5px 10px;
            border-radius: 15px;
            font-size: 12px;
            cursor: pointer;
            border: 1px solid #ced4da;
        }
        .word-tag:hover {
            background-color: #007bff;
            color: white;
        }
        .stats {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 15px;
            margin-top: 20px;
        }
        .stat-item {
            background-color: #f8f9fa;
            padding: 15px;
            border-radius: 5px;
            text-align: center;
        }
        .stat-value {
            font-size: 24px;
            font-weight: bold;
            color: #007bff;
        }
        .stat-label {
            font-size: 12px;
            color: #666;
            margin-top: 5px;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🎤 小智唤醒词配置管理</h1>
        
        <!-- 当前配置显示 -->
        <div class="current-config">
            <h3>📋 当前配置</h3>
            <p><strong>当前唤醒词：</strong><span id="currentWakeWord">加载中...</span></p>
            <button onclick="loadCurrentConfig()">🔄 刷新配置</button>
        </div>

        <!-- 设置新唤醒词 -->
        <div class="section">
            <h3>⚙️ 设置唤醒词</h3>
            <div class="form-group">
                <label for="newWakeWord">新唤醒词：</label>
                <input type="text" id="newWakeWord" placeholder="请输入新的唤醒词，例如：小智、小爱等">
            </div>
            <button onclick="setWakeWord()">💾 设置唤醒词</button>
            <button class="secondary" onclick="loadSupportedWords()">📝 查看支持的唤醒词</button>
            <div id="setWakeWordStatus"></div>
        </div>

        <!-- 支持的唤醒词列表 -->
        <div class="section">
            <h3>📚 支持的唤醒词</h3>
            <p>点击下方的唤醒词可以快速设置：</p>
            <div id="supportedWords" class="supported-words">
                加载中...
            </div>
            <div id="supportedWordsStatus"></div>
        </div>

        <!-- 系统状态 -->
        <div class="section">
            <h3>📊 系统状态</h3>
            <button onclick="getSystemStatus()">🔍 检查系统状态</button>
            <button class="secondary" onclick="forceDisconnect()">⚠️ 强制断开设备</button>
            <div id="systemStatus"></div>
            
            <div class="stats" id="statsContainer">
                <!-- 统计信息将在这里显示 -->
            </div>
        </div>
    </div>

    <script>
        const API_BASE = '/api/voice';

        // 页面加载时初始化
        window.onload = function() {
            loadCurrentConfig();
            loadSupportedWords();
        };

        // 加载当前配置
        async function loadCurrentConfig() {
            try {
                const response = await fetch(`${API_BASE}/wake-word`);
                const data = await response.json();
                
                if (data.success) {
                    document.getElementById('currentWakeWord').textContent = data.wake_word;
                } else {
                    document.getElementById('currentWakeWord').textContent = '加载失败: ' + data.message;
                }
            } catch (error) {
                document.getElementById('currentWakeWord').textContent = '网络错误: ' + error.message;
            }
        }

        // 设置新唤醒词
        async function setWakeWord() {
            const newWakeWord = document.getElementById('newWakeWord').value.trim();
            const statusDiv = document.getElementById('setWakeWordStatus');
            
            if (!newWakeWord) {
                showStatus(statusDiv, '请输入唤醒词', 'error');
                return;
            }

            try {
                const formData = new FormData();
                formData.append('wake_word', newWakeWord);
                
                const response = await fetch(`${API_BASE}/wake-word`, {
                    method: 'POST',
                    body: formData
                });
                
                const data = await response.json();
                
                if (data.success) {
                    showStatus(statusDiv, `✅ ${data.message}`, 'success');
                    document.getElementById('newWakeWord').value = '';
                    loadCurrentConfig(); // 刷新当前配置显示
                } else {
                    showStatus(statusDiv, `❌ ${data.message}`, 'error');
                }
            } catch (error) {
                showStatus(statusDiv, `❌ 网络错误: ${error.message}`, 'error');
            }
        }

        // 加载支持的唤醒词
        async function loadSupportedWords() {
            try {
                const response = await fetch(`${API_BASE}/wake-word/supported`);
                const data = await response.json();
                
                const container = document.getElementById('supportedWords');
                const statusDiv = document.getElementById('supportedWordsStatus');
                
                if (data.success) {
                    container.innerHTML = '';
                    data.supported_wake_words.forEach(word => {
                        const tag = document.createElement('div');
                        tag.className = 'word-tag';
                        tag.textContent = word;
                        tag.onclick = () => {
                            document.getElementById('newWakeWord').value = word;
                        };
                        container.appendChild(tag);
                    });
                    showStatus(statusDiv, `✅ 加载了 ${data.supported_wake_words.length} 个支持的唤醒词`, 'success');
                } else {
                    showStatus(statusDiv, `❌ ${data.message}`, 'error');
                }
            } catch (error) {
                showStatus(document.getElementById('supportedWordsStatus'), `❌ 网络错误: ${error.message}`, 'error');
            }
        }

        // 获取系统状态
        async function getSystemStatus() {
            try {
                const response = await fetch(`${API_BASE}/status`);
                const data = await response.json();
                
                const statusDiv = document.getElementById('systemStatus');
                showStatus(statusDiv, `✅ ${data.message}`, 'success');
                
                // 显示统计信息（这里可以扩展更多统计数据）
                const statsContainer = document.getElementById('statsContainer');
                statsContainer.innerHTML = `
                    <div class="stat-item">
                        <div class="stat-value">运行中</div>
                        <div class="stat-label">服务状态</div>
                    </div>
                    <div class="stat-item">
                        <div class="stat-value">${new Date().toLocaleString()}</div>
                        <div class="stat-label">检查时间</div>
                    </div>
                `;
            } catch (error) {
                showStatus(document.getElementById('systemStatus'), `❌ 网络错误: ${error.message}`, 'error');
            }
        }

        // 强制断开设备连接
        async function forceDisconnect() {
            if (!confirm('确定要强制断开所有设备连接吗？')) {
                return;
            }
            
            try {
                const response = await fetch(`${API_BASE}/force-disconnect`, {
                    method: 'POST'
                });
                
                const data = await response.json();
                const statusDiv = document.getElementById('systemStatus');
                
                if (data.success) {
                    showStatus(statusDiv, `✅ ${data.message}`, 'success');
                } else {
                    showStatus(statusDiv, `❌ ${data.message}`, 'error');
                }
            } catch (error) {
                showStatus(document.getElementById('systemStatus'), `❌ 网络错误: ${error.message}`, 'error');
            }
        }

        // 显示状态消息
        function showStatus(element, message, type) {
            element.innerHTML = `<div class="status ${type}">${message}</div>`;
            
            // 3秒后自动清除成功消息
            if (type === 'success') {
                setTimeout(() => {
                    element.innerHTML = '';
                }, 3000);
            }
        }

        // 回车键快速设置
        document.getElementById('newWakeWord').addEventListener('keypress', function(e) {
            if (e.key === 'Enter') {
                setWakeWord();
            }
        });
    </script>
</body>
</html>
