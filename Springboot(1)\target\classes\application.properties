# 服务器配置
server.address: 0.0.0.0
server.port=8080
spring.application.name=voice-service
server.servlet.context-path=/
server.servlet.encoding.force=true
server.servlet.encoding.charset=UTF-8
spring.mandatory-file-encoding=UTF-8
spring.messages.encoding=UTF-8

# VAD模型配置
vad.model.path=classpath:models/silero_vad.onnx
# VAD长时间静音检测超时时间(毫秒) - 调整为10秒
vad.long.silence.timeout.ms=10000
# VAD调试模式
vad.debug=true

# WebSocket配置
#spring.websocket.message-size-limit=10485760
#spring.websocket.idle-timeout=60000

# 音频配置 - 更新默认帧时长为60ms
audio.sample-rate=16000
audio.channels=1
#audio.format=pcm
audio.frame-duration=60

# OPUS编解码器配置 - 优化为ESP32设备的要求
#audio.opus.bitrate=32000
#audio.opus.complexity=10
#audio.opus.application=2048

# 语音识别配置 (SenseVoice)
sensevoice.api.url=http://localhost:8000/api/v1/asr
#sensevoice.api.timeout=30000
sensevoice.retry.max=3
sensevoice.retry.delay=1000

# 大语言模型配置 - 从Ollama改为VLLM
vllm.api.url=http://************:8002/v1/chat/completions
#vllm.api.url=http://localhost:8002/v1/chat/completions
#vllm.model.name=Qwen3_6B
vllm.model.name=/home/<USER>/qwen
vllm.api.key=
vllm.api.timeout=60000
vllm.max.retries=3
#vllm.retry.delay=1000
vllm.retry.delay=300
vllm.enable.thinking=false
vllm.system-prompt=请多说两句

# 语音合成配置 (CosyVoice)
cosyvoice.api.url=http://localhost:8002
#cosyvoice.api.timeout=30000
cosyvoice.default-speaker=中文女
# 可选音色列表（根据实际可用音色填写）
#cosyvoice.available-speakers=中文女,中文男,日语男,粤语女,英文女,英文男,韩语女
# 音频质量参数，范围1-10，值越高质量越好
cosyvoice.complexity=10

# 临时文件路径
temp.dir=${java.io.tmpdir}/voice-service

# 日志配置
logging.file.name=logs/voice-service.log
logging.logback.rollingpolicy.max-file-size=10MB
logging.logback.rollingpolicy.max-history=10
# 线程池配置
thread-pool.core-size=10
thread-pool.max-size=30
thread-pool.queue-capacity=100
thread-pool.keep-alive-seconds=300

# MQTT配置 - 注释掉默认配置，避免启动失败
# mqtt.broker.url=tcp://localhost:1883
#mqtt.client.id=xiaozhi_server_
#mqtt.username=
#mqtt.password=
#mqtt.subscribe.topic=device/+/+
#mqtt.publish.topic=server/

# 音频编解码配置 - 与前面保持一致的帧时长
#audio.codec.sample-rate=16000
#audio.codec.channels=1
#audio.codec.bit-rate=24000
#audio.codec.frame-duration=60

# 日志配置
logging.level.root=INFO
logging.level.com.example=DEBUG
logging.pattern.console=%d{yyyy-MM-dd HH:mm:ss} [%thread] %-5level %logger{36} - %msg%n

# UDP服务器配置
udp.server.port=12345
#udp.buffer.size=4096