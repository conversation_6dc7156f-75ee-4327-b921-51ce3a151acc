package com.example.service;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import com.example.utils.OpusProcessor;
import com.example.vad.impl.SileroVadModel;

import io.github.jaredmdobson.concentus.OpusException;
import jakarta.annotation.PostConstruct;
import jakarta.annotation.PreDestroy;
import java.io.ByteArrayOutputStream;
import java.io.IOException;
import java.nio.ByteBuffer;
import java.nio.ByteOrder;
import java.util.ArrayList;
import java.util.LinkedList;
import java.util.List;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;

/**
 * 语音活动检测服务
 * 用于检测音频流中的语音活动，包括语音开始和结束
 */
@Service
public class VadService {
    private static final Logger logger = LoggerFactory.getLogger(VadService.class);
    
    @Autowired
    private OpusProcessor opusProcessor;
    
    @Autowired
    private SileroVadModel vadModel;
    
    // 语音检测前缓冲时长(毫秒)
    private int preBufferMs = 500;
    
    // 会话状态映射
    private final Map<String, VadState> states = new ConcurrentHashMap<>();
    private final Map<String, Object> locks = new ConcurrentHashMap<>();
    
    // 最小PCM长度阈值(约100ms的16kHz 16bit音频)
    private static final int MIN_PCM_LENGTH = 1600;
    
    // VAD模型所需的样本大小
    private static final int VAD_SAMPLE_SIZE = 512;
    
    // VAD参数
    private static final float SPEECH_THRESHOLD = 0.5f;   // 语音检测阈值
    private static final float SILENCE_THRESHOLD = 0.3f;  // 静音检测阈值
    private static final float ENERGY_THRESHOLD = 0.001f; // 能量检测阈值
    private static final int SILENCE_TIMEOUT_MS = 1200;   // 静音超时时间(毫秒)
    
    // 长时间静音超时参数 - 默认5秒
    @Value("${vad.long.silence.timeout.ms:5000}")
    private int longSilenceTimeoutMs;
    
    @PostConstruct
    public void init() {
        try {
            if (vadModel != null) {
                logger.info("VAD服务初始化成功");
                logger.info("长时间静音检测超时时间: {}ms", longSilenceTimeoutMs);
            } else {
                logger.error("SileroVadModel未注入，VAD功能不可用");
            }
        } catch (Exception e) {
            logger.error("初始化VAD服务失败", e);
        }
    }
    
    @PreDestroy
    public void cleanup() {
        logger.info("VAD服务资源已释放");
        states.clear();
        locks.clear();
    }
    
    /**
     * 简化的会话状态类
     */
    private class VadState {
        // 语音状态
        private boolean speaking = false;
        private long speechTime = 0;
        private long silenceTime = 0;
        private long lastAudioReceivedTime = System.currentTimeMillis();
        
        // 检测告别词
        private boolean goodbyeDetected = false;
        
        // 音频分析
        private float avgEnergy = 0;
        private final List<Float> probs = new ArrayList<>();
        
        // 添加原始VAD概率列表
        private final List<Float> originalProbs = new ArrayList<>();
        
        // 帧计数器（用于每10帧输出一次）
        private int frameCounter = 0;

        // 预缓冲
        private final LinkedList<byte[]> preBuffer = new LinkedList<>();
        private int preBufferSize = 0;
        private final int maxPreBufferSize;

        // 音频数据
        private final List<byte[]> pcmData = new ArrayList<>();
        private final List<byte[]> opusData = new ArrayList<>();

        // 短帧累积
        private final ByteArrayOutputStream pcmAccumulator = new ByteArrayOutputStream();
        private long lastAccumTime = 0;

        public VadState() {
            this.maxPreBufferSize = preBufferMs * 32; // 16kHz, 16bit, mono = 32 bytes/ms
            this.lastAccumTime = System.currentTimeMillis();
            // 将最后音频接收时间初始化为当前时间减去一半的超时时间，这样如果没有检测到有效音频，
            // 系统会在超时时间的一半后开始断开连接
            this.lastAudioReceivedTime = System.currentTimeMillis() - (longSilenceTimeoutMs / 2);
        }

        public boolean isSpeaking() {
            return speaking;
        }

        public void setSpeaking(boolean speaking) {
            this.speaking = speaking;
            if (speaking) {
                speechTime = System.currentTimeMillis();
                silenceTime = 0;
            } else if (silenceTime == 0) {
                silenceTime = System.currentTimeMillis();
            }
        }

        public int getSilenceDuration() {
            return silenceTime == 0 ? 0 : (int) (System.currentTimeMillis() - silenceTime);
        }
        
        public int getTimeSinceLastAudio() {
            return (int) (System.currentTimeMillis() - lastAudioReceivedTime);
        }
        
        public void updateLastAudioTime() {
            long previousTime = lastAudioReceivedTime;
            lastAudioReceivedTime = System.currentTimeMillis();
            
            // 每5秒记录一次音频接收时间，用于调试
            if (System.currentTimeMillis() - previousTime > 5000) {
                logger.info("更新音频接收时间: 线程={}, 距上次更新={}ms", 
                           Thread.currentThread().getName(), System.currentTimeMillis() - previousTime);
            }
        }
        
        public boolean isGoodbyeDetected() {
            return goodbyeDetected;
        }
        
        public void setGoodbyeDetected(boolean detected) {
            this.goodbyeDetected = detected;
        }

        public void updateSilence(boolean isSilent) {
            if (isSilent) {
                if (silenceTime == 0) {
                    silenceTime = System.currentTimeMillis();
                }
            } else {
                silenceTime = 0;
            }
        }

        public void updateEnergy(float energy) {
            avgEnergy = (avgEnergy == 0) ? energy : 0.95f * avgEnergy + 0.05f * energy;
        }

        public float getAvgEnergy() {
            return avgEnergy;
        }

        public void addProb(float prob) {
            probs.add(prob);
            if (probs.size() > 10) {
                probs.remove(0);
            }
        }
        
        // 添加原始VAD概率
        public void addOriginalProb(float prob) {
            originalProbs.add(prob);
            if (originalProbs.size() > 10) {
                originalProbs.remove(0);
            }
            
            // 增加帧计数器
            frameCounter++;
        }
        
        public float getLastOriginalProb() {
            return originalProbs.isEmpty() ? 0.0f : originalProbs.get(originalProbs.size() - 1);
        }

        public float getLastProb() {
            return probs.isEmpty() ? 0.0f : probs.get(probs.size() - 1);
        }
        
        public int getFrameCounter() {
            return frameCounter;
        }

        // 预缓冲区管理
        public void addToPreBuffer(byte[] data) {
            if (speaking)
                return;
                
            preBuffer.add(data.clone());
            preBufferSize += data.length;
            
            while (preBufferSize > maxPreBufferSize && !preBuffer.isEmpty()) {
                byte[] removed = preBuffer.removeFirst();
                preBufferSize -= removed.length;
            }
        }
        
        public byte[] drainPreBuffer() {
            if (preBuffer.isEmpty())
                return new byte[0];
                
            ByteArrayOutputStream outputStream = new ByteArrayOutputStream();
            for (byte[] data : preBuffer) {
                try {
                    outputStream.write(data);
                } catch (IOException e) {
                    logger.error("合并预缓冲区数据失败", e);
                }
            }
            
            preBuffer.clear();
            preBufferSize = 0;
            
            return outputStream.toByteArray();
        }
        
        public void accumulate(byte[] pcm) {
            try {
                pcmAccumulator.write(pcm);
                lastAccumTime = System.currentTimeMillis();
            } catch (IOException e) {
                logger.error("累积PCM数据失败", e);
            }
        }
        
        public byte[] drainAccumulator() {
            byte[] data = pcmAccumulator.toByteArray();
            pcmAccumulator.reset();
            return data;
        }
        
        public int getAccumSize() {
            return pcmAccumulator.size();
        }
        
        public boolean isAccumTimedOut() {
            return System.currentTimeMillis() - lastAccumTime > 200;  // 200ms超时
        }
        
        public void addPcm(byte[] pcm) {
            pcmData.add(pcm.clone());
        }
        
        public void addOpus(byte[] opus) {
            opusData.add(opus.clone());
        }
        
        public List<byte[]> getPcmData() {
            return pcmData;
        }
        
        public List<byte[]> getOpusData() {
            return opusData;
        }
        
        public void reset() {
            speaking = false;
            speechTime = 0;
            silenceTime = 0;
            // 重置最后音频接收时间，使其比当前时间早一半的超时时间
            lastAudioReceivedTime = System.currentTimeMillis() - (longSilenceTimeoutMs / 2);
            goodbyeDetected = false;
            avgEnergy = 0;
            probs.clear();
            originalProbs.clear();
            frameCounter = 0;
            preBuffer.clear();
            preBufferSize = 0;
            pcmData.clear();
            opusData.clear();
            pcmAccumulator.reset();
            lastAccumTime = System.currentTimeMillis();
        }
        
        // 强制重置最后音频接收时间，用于测试
        public void forceResetLastAudioTime() {
            lastAudioReceivedTime = System.currentTimeMillis() - longSilenceTimeoutMs;
            logger.info("强制重置最后音频接收时间: {}", lastAudioReceivedTime);
        }
    }
    
    /**
     * 初始化会话
     */
    public void initSession(String sessionId) {
        states.computeIfAbsent(sessionId, k -> new VadState());
        locks.computeIfAbsent(sessionId, k -> new Object());
    }
    
    /**
     * 检查会话是否初始化
     */
    public boolean isSessionInitialized(String sessionId) {
        return states.containsKey(sessionId);
    }
    
    /**
     * 获取会话锁
     */
    private Object getLock(String sessionId) {
        return locks.computeIfAbsent(sessionId, k -> new Object());
    }
    
    /**
     * 处理音频数据，检测语音活动
     */
    public VadResult processAudio(String sessionId, byte[] opusData) {
        if (!isSessionInitialized(sessionId)) {
            initSession(sessionId);
        }
        
        Object lock = getLock(sessionId);
        
        synchronized (lock) {
            try {
                // 获取会话状态
                VadState state = states.get(sessionId);
                
                // 保存原始Opus数据
                state.addOpus(opusData);
                
                // 解码Opus数据
                byte[] pcmData;
                try {
                    pcmData = opusProcessor.opusToPcm(sessionId, opusData);
                } catch (Exception e) {
                    logger.warn("解码Opus数据失败: {}", e.getMessage());
                    return new VadResult(VadStatus.NO_SPEECH, null);
                }
                
                if (pcmData == null || pcmData.length == 0) {
                    return new VadResult(VadStatus.NO_SPEECH, null);
                }
                
                // 添加到预缓冲区
                state.addToPreBuffer(pcmData);
                
                // 处理短帧数据
                if (pcmData.length < MIN_PCM_LENGTH && !state.isSpeaking()) {
                    state.accumulate(pcmData);
                    
                    // 检查是否需要继续累积
                    if (state.getAccumSize() < MIN_PCM_LENGTH && !state.isAccumTimedOut()) {
                        return new VadResult(VadStatus.NO_SPEECH, null);
                    }
                    
                    // 处理累积的数据
                    pcmData = state.drainAccumulator();
                    if (pcmData.length == 0) {
                        return new VadResult(VadStatus.NO_SPEECH, null);
                    }
                }
                
                // 分析音频
                float[] samples = bytesToFloats(pcmData);
                float energy = calcEnergy(samples);
                state.updateEnergy(energy);
                
                // 准备VAD输入
                float[] vadSamples = prepareVadSamples(samples);
                
                // 使用Silero VAD模型进行检测
                float speechProb = vadModel.getSpeechProbability(vadSamples);
                state.addProb(speechProb);
                state.addOriginalProb(speechProb);
                
                // 每10帧输出一次日志
                if (state.getFrameCounter() % 10 == 0) {
                    logger.debug("VAD概率: {}, 能量: {}, 平均能量: {}", 
                               String.format("%.3f", speechProb), 
                               String.format("%.6f", energy),
                               String.format("%.6f", state.getAvgEnergy()));
                }
                
                // 判断语音状态
                boolean hasEnergy = energy > state.getAvgEnergy() * 1.5 && energy > ENERGY_THRESHOLD;
                boolean isSpeech = speechProb > SPEECH_THRESHOLD && hasEnergy;
                boolean isSilence = speechProb < SILENCE_THRESHOLD;
                state.updateSilence(isSilence);
                
                // 只有在检测到语音活动或能量超过阈值时才更新最后音频时间
                if (isSpeech || energy > ENERGY_THRESHOLD * 10) {
                    state.updateLastAudioTime();
                    logger.debug("检测到有效音频，更新最后音频时间: {}", sessionId);
                }
                
                // 处理状态转换
                if (!state.isSpeaking() && isSpeech) {
                    // 语音开始
                    state.pcmData.clear();
                    state.setSpeaking(true);
                    // 确保设置语音开始时间
                    state.speechTime = System.currentTimeMillis();
                    state.silenceTime = 0;
                    
                    logger.info("检测到语音开始 - SessionId: {}, 概率: {}, 能量: {}", 
                               sessionId, speechProb, energy);
                               
                    // 获取预缓冲数据
                    byte[] preBufferData = state.drainPreBuffer();
                    byte[] result;
                    
                    if (preBufferData.length > 0) {
                        // 使用改进的连接方法
                        ByteArrayOutputStream outputStream = new ByteArrayOutputStream();
                        try {
                            // 首先写入预缓冲数据
                            outputStream.write(preBufferData);
                            
                            // 然后写入当前帧
                            outputStream.write(pcmData);
                            
                            result = outputStream.toByteArray();
                        } catch (IOException e) {
                            logger.error("合并音频数据失败: {}", e.getMessage());
                            result = pcmData;
                        }
                        
                        state.addPcm(result);
                    } else {
                        result = pcmData;
                        state.addPcm(pcmData);
                    }
                    
                    return new VadResult(VadStatus.SPEECH_START, result);
                } else if (state.isSpeaking() && isSilence) {
                    // 检查静音时长
                    int silenceDuration = state.getSilenceDuration();
                    if (silenceDuration > SILENCE_TIMEOUT_MS) {
                        // 语音结束
                        state.setSpeaking(false);
                        logger.info("语音结束: {}, 静音: {}ms", sessionId, silenceDuration);
                        return new VadResult(VadStatus.SPEECH_END, pcmData);
                    } else {
                        // 继续收集
                        state.addPcm(pcmData);
                        return new VadResult(VadStatus.SPEECH_CONTINUE, pcmData);
                    }
                } else if (state.isSpeaking()) {
                    // 语音继续
                    state.addPcm(pcmData);
                    return new VadResult(VadStatus.SPEECH_CONTINUE, pcmData);
                } else {
                    // 无语音
                    return new VadResult(VadStatus.NO_SPEECH, null);
                }
            } catch (Exception e) {
                logger.error("处理音频失败: {}, 错误: {}", sessionId, e.getMessage(), e);
                return new VadResult(VadStatus.ERROR, null);
            }
        }
    }
    
    /**
     * 准备VAD样本
     * 将任意长度的样本调整为VAD模型所需的固定长度
     */
    private float[] prepareVadSamples(float[] samples) {
        float[] vadSamples = new float[VAD_SAMPLE_SIZE];
        
        if (samples.length >= VAD_SAMPLE_SIZE) {
            // 如果样本足够长，取中间部分
            int startIdx = (samples.length - VAD_SAMPLE_SIZE) / 2;
            System.arraycopy(samples, startIdx, vadSamples, 0, VAD_SAMPLE_SIZE);
        } else {
            // 如果样本不够长，居中填充
            int padding = (VAD_SAMPLE_SIZE - samples.length) / 2;
            System.arraycopy(samples, 0, vadSamples, padding, samples.length);
        }
        
        return vadSamples;
    }
    
    /**
     * 计算音频能量
     */
    private float calcEnergy(float[] samples) {
        float sum = 0;
        for (float sample : samples) {
            sum += sample * sample;
        }
        return samples.length > 0 ? sum / samples.length : 0;
    }
    
    /**
     * 将PCM字节数据转换为浮点数组
     */
    private float[] bytesToFloats(byte[] pcmData) {
        float[] samples = new float[pcmData.length / 2]; // 16bit = 2字节/样本
        
        short[] shorts = new short[samples.length];
        ByteBuffer.wrap(pcmData).order(ByteOrder.LITTLE_ENDIAN).asShortBuffer().get(shorts);
        
        for (int i = 0; i < samples.length; i++) {
            samples[i] = shorts[i] / 32768.0f; // 转为-1.0到1.0之间的值
        }
        return samples;
    }
    
    /**
     * 重置会话
     */
    public void resetSession(String sessionId) {
        synchronized (getLock(sessionId)) {
            VadState state = states.get(sessionId);
            if (state != null) {
                state.reset();
            }
        }
    }
    
    /**
     * 检查是否正在说话
     */
    public boolean isSpeaking(String sessionId) {
        synchronized (getLock(sessionId)) {
            VadState state = states.get(sessionId);
            return state != null && state.isSpeaking();
        }
    }
    
    /**
     * 检查是否长时间无语音活动(超过5秒)
     */
    public boolean isLongSilenceDetected(String sessionId) {
        synchronized (getLock(sessionId)) {
            VadState state = states.get(sessionId);
            if (state == null) {
                logger.warn("无法检测长时间静音: 会话状态为null, SessionId={}", sessionId);
                return false;
            }
            
            // 检查自上次收到音频数据后是否已超过设定的静音时间
            int timeSinceLastAudio = state.getTimeSinceLastAudio();
            
            // 每秒记录一次当前静音时间
            if (timeSinceLastAudio > 1000 && timeSinceLastAudio % 1000 < 100) {
                logger.info("静音时间统计: SessionId={}, 当前静音时长={}ms, 超时阈值={}ms", 
                           sessionId, timeSinceLastAudio, longSilenceTimeoutMs);
            }
            
            if (timeSinceLastAudio > longSilenceTimeoutMs) {
                logger.info("检测到长时间静音: SessionId={}, 静音时长={}ms, 超时阈值={}ms", 
                          sessionId, timeSinceLastAudio, longSilenceTimeoutMs);
                return true;
            }
            
            return false;
        }
    }
    
    /**
     * 设置告别词检测标志
     */
    public void setGoodbyeDetected(String sessionId, boolean detected) {
        synchronized (getLock(sessionId)) {
            VadState state = states.get(sessionId);
            if (state != null) {
                state.setGoodbyeDetected(detected);
                if (detected) {
                    logger.info("检测到告别词: SessionId={}", sessionId);
                }
            }
        }
    }
    
    /**
     * 检查是否检测到告别词
     */
    public boolean isGoodbyeDetected(String sessionId) {
        synchronized (getLock(sessionId)) {
            VadState state = states.get(sessionId);
            return state != null && state.isGoodbyeDetected();
        }
    }
    
    /**
     * 获取语音概率
     */
    public float getSpeechProbability(String sessionId) {
        synchronized (getLock(sessionId)) {
            VadState state = states.get(sessionId);
            return state != null ? state.getLastProb() : 0.0f;
        }
    }
    
    /**
     * 获取原始语音概率
     */
    public float getOriginalSpeechProbability(String sessionId) {
        synchronized (getLock(sessionId)) {
            VadState state = states.get(sessionId);
            return state != null ? state.getLastOriginalProb() : 0.0f;
        }
    }
    
    /**
     * 获取所有PCM数据
     */
    public List<byte[]> getPcmData(String sessionId) {
        synchronized (getLock(sessionId)) {
            VadState state = states.get(sessionId);
            return state != null ? new ArrayList<>(state.getPcmData()) : new ArrayList<>();
        }
    }
    
    /**
     * 获取所有Opus数据
     */
    public List<byte[]> getOpusData(String sessionId) {
        synchronized (getLock(sessionId)) {
            VadState state = states.get(sessionId);
            return state != null ? new ArrayList<>(state.getOpusData()) : new ArrayList<>();
        }
    }
    
    /**
     * 强制重置所有会话的最后音频接收时间，用于测试
     */
    public void forceResetAllSessionsLastAudioTime() {
        logger.info("强制重置所有会话的最后音频接收时间");
        for (Map.Entry<String, VadState> entry : states.entrySet()) {
            String sessionId = entry.getKey();
            VadState state = entry.getValue();
            synchronized (getLock(sessionId)) {
                state.forceResetLastAudioTime();
            }
        }
    }
    
    /**
     * VAD状态枚举
     */
    public enum VadStatus {
        NO_SPEECH,       // 无语音
        SPEECH_START,    // 语音开始
        SPEECH_CONTINUE, // 语音继续
        SPEECH_END,      // 语音结束
        ERROR            // 处理错误
    }
    
    /**
     * VAD结果类
     */
    public static class VadResult {
        private final VadStatus status;
        private final byte[] data;
        
        public VadResult(VadStatus status, byte[] data) {
            this.status = status;
            this.data = data;
        }
        
        public VadStatus getStatus() {
            return status;
        }
        
        public byte[] getProcessedData() {
            return data;
        }
        
        public boolean isSpeechActive() {
            return status == VadStatus.SPEECH_START || status == VadStatus.SPEECH_CONTINUE;
        }
        
        public boolean isSpeechEnd() {
            return status == VadStatus.SPEECH_END;
        }
    }
} 