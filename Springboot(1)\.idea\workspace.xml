<?xml version="1.0" encoding="UTF-8"?>
<project version="4">
  <component name="AutoImportSettings">
    <option name="autoReloadType" value="SELECTIVE" />
  </component>
  <component name="ChangeListManager">
    <list default="true" id="9367aeea-157c-4c91-98f8-6f621b0838aa" name="更改" comment="" />
    <option name="SHOW_DIALOG" value="false" />
    <option name="HIGHLIGHT_CONFLICTS" value="true" />
    <option name="HIGHLIGHT_NON_ACTIVE_CHANGELIST" value="false" />
    <option name="LAST_RESOLUTION" value="IGNORE" />
  </component>
  <component name="FileTemplateManagerImpl">
    <option name="RECENT_TEMPLATES">
      <list>
        <option value="Interface" />
        <option value="Class" />
      </list>
    </option>
  </component>
  <component name="Git.Settings">
    <option name="RECENT_GIT_ROOT_PATH" value="$PROJECT_DIR$/.." />
  </component>
  <component name="ProblemsViewState">
    <option name="selectedTabId" value="CurrentFile" />
  </component>
  <component name="ProjectColorInfo">{
  &quot;associatedIndex&quot;: 6
}</component>
  <component name="ProjectId" id="2vU6yHr0l5j4c49DPT3lsMB0e3N" />
  <component name="ProjectViewState">
    <option name="hideEmptyMiddlePackages" value="true" />
    <option name="showLibraryContents" value="true" />
  </component>
  <component name="PropertiesComponent">{
  &quot;keyToString&quot;: {
    &quot;Maven.voice-service [clean].executor&quot;: &quot;Run&quot;,
    &quot;RequestMappingsPanelOrder0&quot;: &quot;0&quot;,
    &quot;RequestMappingsPanelOrder1&quot;: &quot;1&quot;,
    &quot;RequestMappingsPanelWidth0&quot;: &quot;75&quot;,
    &quot;RequestMappingsPanelWidth1&quot;: &quot;75&quot;,
    &quot;RunOnceActivity.ShowReadmeOnStart&quot;: &quot;true&quot;,
    &quot;Spring Boot.Application.executor&quot;: &quot;Run&quot;,
    &quot;Spring Boot.CosyVoiceApplication.executor&quot;: &quot;Run&quot;,
    &quot;Spring Boot.Main (1).executor&quot;: &quot;Run&quot;,
    &quot;Spring Boot.Main.executor&quot;: &quot;Run&quot;,
    &quot;jdk.selected.JAVA_MODULE&quot;: &quot;ms-21&quot;,
    &quot;kotlin-language-version-configured&quot;: &quot;true&quot;,
    &quot;last_opened_file_path&quot;: &quot;E:/Learn/Cosy__Test/wav传输测试/存档/0711除了语音断开/aaa&quot;,
    &quot;node.js.detected.package.eslint&quot;: &quot;true&quot;,
    &quot;node.js.detected.package.tslint&quot;: &quot;true&quot;,
    &quot;node.js.selected.package.eslint&quot;: &quot;(autodetect)&quot;,
    &quot;node.js.selected.package.tslint&quot;: &quot;(autodetect)&quot;,
    &quot;nodejs_package_manager_path&quot;: &quot;npm&quot;,
    &quot;project.structure.last.edited&quot;: &quot;项目&quot;,
    &quot;project.structure.proportion&quot;: &quot;0.15&quot;,
    &quot;project.structure.side.proportion&quot;: &quot;0.2&quot;,
    &quot;settings.editor.selected.configurable&quot;: &quot;File.Encoding&quot;,
    &quot;ts.external.directory.path&quot;: &quot;D:\\Learn\\Jet Brains2024\\idea2024\\IntelliJ IDEA 2024.1.2\\plugins\\javascript-plugin\\jsLanguageServicesImpl\\external&quot;,
    &quot;vue.rearranger.settings.migration&quot;: &quot;true&quot;,
    &quot;应用程序.OpusTest.executor&quot;: &quot;Run&quot;,
    &quot;应用程序.PcmToOpusTest.executor&quot;: &quot;Run&quot;
  }
}</component>
  <component name="RecentsManager">
    <key name="CopyFile.RECENT_KEYS">
      <recent name="E:\Learn\Cosy__Test\Springboot\src\main\java\com\example\config" />
      <recent name="E:\Learn\Cosy__Test\Springboot\src\main\java\com\example\service" />
      <recent name="E:\Learn\Cosy__Test\Springboot\src\main\java\com\example\controller" />
      <recent name="E:\Projects\ideaProjects\Git\git_Projects\CosyVoice\src\main\java\com\example\service" />
    </key>
    <key name="ExtractSuperBase.RECENT_KEYS">
      <recent name="com.example.service" />
    </key>
  </component>
  <component name="RunManager">
    <configuration name="Application" type="SpringBootApplicationConfigurationType" factoryName="Spring Boot" temporary="true" nameIsGenerated="true">
      <option name="FRAME_DEACTIVATION_UPDATE_POLICY" value="UpdateClassesAndResources" />
      <module name="voice-service" />
      <option name="SPRING_BOOT_MAIN_CLASS" value="com.example.Application" />
      <extension name="coverage">
        <pattern>
          <option name="PATTERN" value="com.example.*" />
          <option name="ENABLED" value="true" />
        </pattern>
      </extension>
      <method v="2">
        <option name="Make" enabled="true" />
      </method>
    </configuration>
    <recent_temporary>
      <list>
        <item itemvalue="Spring Boot.Application" />
      </list>
    </recent_temporary>
  </component>
  <component name="SharedIndexes">
    <attachedChunks>
      <set>
        <option value="bundled-jdk-9f38398b9061-39b83d9b5494-intellij.indexing.shared.core-IU-241.17011.79" />
        <option value="bundled-js-predefined-1d06a55b98c1-0b3e54e931b4-JavaScript-IU-241.17011.79" />
      </set>
    </attachedChunks>
  </component>
  <component name="SpellCheckerSettings" RuntimeDictionaries="0" Folders="0" CustomDictionaries="0" DefaultDictionary="应用程序级" UseSingleDictionary="true" transferred="true" />
  <component name="TaskManager">
    <task active="true" id="Default" summary="默认任务">
      <changelist id="9367aeea-157c-4c91-98f8-6f621b0838aa" name="更改" comment="" />
      <created>1744187561464</created>
      <option name="number" value="Default" />
      <option name="presentableId" value="Default" />
      <updated>1744187561464</updated>
      <workItem from="1744187562546" duration="1198000" />
      <workItem from="1744439621074" duration="27000" />
      <workItem from="1744540616667" duration="9588000" />
      <workItem from="1744591606653" duration="4797000" />
      <workItem from="1744639463963" duration="675000" />
      <workItem from="1744721397032" duration="1724000" />
      <workItem from="1744761692310" duration="5080000" />
      <workItem from="1744785699491" duration="2223000" />
      <workItem from="1744790567556" duration="1543000" />
      <workItem from="1744795907775" duration="641000" />
      <workItem from="1744807258286" duration="3635000" />
      <workItem from="1744847472875" duration="1145000" />
      <workItem from="1744854530062" duration="4748000" />
      <workItem from="1745196520186" duration="2076000" />
      <workItem from="1745324888797" duration="1017000" />
      <workItem from="1745394422074" duration="3700000" />
      <workItem from="1745412370558" duration="5839000" />
      <workItem from="1745419482271" duration="925000" />
      <workItem from="1745420475179" duration="4973000" />
      <workItem from="1745917028712" duration="2117000" />
      <workItem from="1748315266979" duration="838000" />
      <workItem from="1748389976062" duration="1708000" />
      <workItem from="1748440491690" duration="17000" />
      <workItem from="1749082227522" duration="2145000" />
      <workItem from="1750043203845" duration="4305000" />
      <workItem from="1751422910034" duration="53000" />
      <workItem from="1751422973305" duration="36000" />
      <workItem from="1751423022428" duration="51000" />
      <workItem from="1751423104741" duration="7000" />
      <workItem from="1751424625168" duration="114000" />
      <workItem from="1751436399028" duration="47000" />
      <workItem from="1751436453285" duration="8000" />
      <workItem from="1751436902176" duration="5404000" />
      <workItem from="1751443117806" duration="35621000" />
      <workItem from="1751593337325" duration="236000" />
      <workItem from="1751593584771" duration="263000" />
      <workItem from="1751593859874" duration="546000" />
      <workItem from="1751594418356" duration="49842000" />
      <workItem from="1751974773488" duration="5488000" />
      <workItem from="1752107766785" duration="8131000" />
      <workItem from="1752453938967" duration="21610000" />
      <workItem from="1752623665169" duration="7443000" />
      <workItem from="1752666412294" duration="6326000" />
      <workItem from="1752711683154" duration="2747000" />
      <workItem from="1752767979397" duration="64000" />
    </task>
    <servers />
  </component>
  <component name="TypeScriptGeneratedFilesManager">
    <option name="version" value="3" />
  </component>
  <component name="XSLT-Support.FileAssociations.UIState">
    <expand />
    <select />
  </component>
</project>