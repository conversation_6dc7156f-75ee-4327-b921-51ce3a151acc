package com.example;

import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.scheduling.annotation.EnableAsync;
import org.springframework.scheduling.annotation.EnableScheduling;

/**
 * 语音服务应用程序入口
 */
@SpringBootApplication
@EnableAsync
@EnableScheduling
public class Application {

    public static void main(String[] args) {
        SpringApplication.run(Application.class, args);
        System.out.println("=========================================");
        System.out.println("  智能语音对话服务已启动");
        System.out.println("  - WebSocket 网页端端点: /api/voice/ws");
        System.out.println("  - WebSocket ESP32端点: /api/device/ws");
        System.out.println("=========================================");
    }
} 