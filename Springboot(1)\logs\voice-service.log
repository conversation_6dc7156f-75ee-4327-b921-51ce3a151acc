2025-07-22T21:13:45.078+08:00  INFO 30420 --- [voice-service] [restartedMain] com.example.Application                  : Starting Application using Java 23 with PID 30420 (E:\xiaozhi-esp32-main\Springboot(1)\target\classes started by 31947 in E:\xiaozhi-esp32-main\Springboot(1))
2025-07-22T21:13:45.085+08:00 DEBUG 30420 --- [voice-service] [restartedMain] com.example.Application                  : Running with Spring Boot v3.4.3, Spring v6.2.3
2025-07-22T21:13:45.086+08:00  INFO 30420 --- [voice-service] [restartedMain] com.example.Application                  : No active profile set, falling back to 1 default profile: "default"
2025-07-22T21:13:45.212+08:00  INFO 30420 --- [voice-service] [restartedMain] .e.DevToolsPropertyDefaultsPostProcessor : Devtools property defaults active! Set 'spring.devtools.add-properties' to 'false' to disable
2025-07-22T21:13:45.214+08:00  INFO 30420 --- [voice-service] [restartedMain] .e.DevToolsPropertyDefaultsPostProcessor : For additional web related logging consider setting the 'logging.level.web' property to 'DEBUG'
2025-07-22T21:13:47.027+08:00  INFO 30420 --- [voice-service] [restartedMain] o.s.b.w.embedded.tomcat.TomcatWebServer  : Tomcat initialized with port 8080 (http)
2025-07-22T21:13:47.050+08:00  INFO 30420 --- [voice-service] [restartedMain] o.apache.catalina.core.StandardService   : Starting service [Tomcat]
2025-07-22T21:13:47.051+08:00  INFO 30420 --- [voice-service] [restartedMain] o.apache.catalina.core.StandardEngine    : Starting Servlet engine: [Apache Tomcat/10.1.36]
2025-07-22T21:13:47.112+08:00  INFO 30420 --- [voice-service] [restartedMain] o.a.c.c.C.[Tomcat].[localhost].[/]       : Initializing Spring embedded WebApplicationContext
2025-07-22T21:13:47.112+08:00  INFO 30420 --- [voice-service] [restartedMain] w.s.c.ServletWebServerApplicationContext : Root WebApplicationContext: initialization completed in 1897 ms
2025-07-22T21:13:47.512+08:00  INFO 30420 --- [voice-service] [restartedMain] com.example.vad.impl.SileroVadModel      : 正在加载VAD模型: E:\xiaozhi-esp32-main\Springboot(1)\target\classes\models\silero_vad.onnx
2025-07-22T21:13:47.516+08:00  INFO 16036 --- [voice-service] [restartedMain] com.example.Application                  : Starting Application using Java 23 with PID 16036 (E:\xiaozhi-esp32-main\Springboot(1)\target\classes started by 31947 in E:\xiaozhi-esp32-main\Springboot(1))
2025-07-22T21:13:47.518+08:00 DEBUG 16036 --- [voice-service] [restartedMain] com.example.Application                  : Running with Spring Boot v3.4.3, Spring v6.2.3
2025-07-22T21:13:47.518+08:00  INFO 16036 --- [voice-service] [restartedMain] com.example.Application                  : No active profile set, falling back to 1 default profile: "default"
2025-07-22T21:13:47.580+08:00  INFO 16036 --- [voice-service] [restartedMain] .e.DevToolsPropertyDefaultsPostProcessor : Devtools property defaults active! Set 'spring.devtools.add-properties' to 'false' to disable
2025-07-22T21:13:47.581+08:00  INFO 16036 --- [voice-service] [restartedMain] .e.DevToolsPropertyDefaultsPostProcessor : For additional web related logging consider setting the 'logging.level.web' property to 'DEBUG'
2025-07-22T21:13:47.642+08:00  INFO 30420 --- [voice-service] [restartedMain] com.example.vad.impl.SileroVadModel      : Silero VAD模型初始化成功
2025-07-22T21:13:47.644+08:00  INFO 30420 --- [voice-service] [restartedMain] com.example.service.VadService           : VAD服务初始化成功
2025-07-22T21:13:47.644+08:00  INFO 30420 --- [voice-service] [restartedMain] com.example.service.VadService           : 长时间静音检测超时时间: 10000ms
2025-07-22T21:13:47.843+08:00  INFO 30420 --- [voice-service] [restartedMain] o.s.b.a.w.s.WelcomePageHandlerMapping    : Adding welcome page: class path resource [static/index.html]
2025-07-22T21:13:48.408+08:00  INFO 30420 --- [voice-service] [restartedMain] o.s.b.d.a.OptionalLiveReloadServer       : LiveReload server is running on port 35729
2025-07-22T21:13:48.418+08:00  INFO 30420 --- [voice-service] [restartedMain] o.s.b.a.e.web.EndpointLinksResolver      : Exposing 1 endpoint beneath base path '/actuator'
2025-07-22T21:13:48.496+08:00  INFO 30420 --- [voice-service] [restartedMain] o.s.b.w.embedded.tomcat.TomcatWebServer  : Tomcat started on port 8080 (http) with context path '/'
2025-07-22T21:13:48.510+08:00  INFO 30420 --- [voice-service] [restartedMain] com.example.Application                  : Started Application in 3.928 seconds (process running for 4.796)
2025-07-22T21:13:48.556+08:00  INFO 30420 --- [voice-service] [http-nio-0.0.0.0-8080-exec-1] o.a.c.c.C.[Tomcat].[localhost].[/]       : Initializing Spring DispatcherServlet 'dispatcherServlet'
2025-07-22T21:13:48.557+08:00  INFO 30420 --- [voice-service] [http-nio-0.0.0.0-8080-exec-1] o.s.web.servlet.DispatcherServlet        : Initializing Servlet 'dispatcherServlet'
2025-07-22T21:13:48.558+08:00  INFO 30420 --- [voice-service] [http-nio-0.0.0.0-8080-exec-1] o.s.web.servlet.DispatcherServlet        : Completed initialization in 1 ms
2025-07-22T21:13:49.173+08:00  INFO 16036 --- [voice-service] [restartedMain] o.s.b.w.embedded.tomcat.TomcatWebServer  : Tomcat initialized with port 8080 (http)
2025-07-22T21:13:49.200+08:00  INFO 16036 --- [voice-service] [restartedMain] o.apache.catalina.core.StandardService   : Starting service [Tomcat]
2025-07-22T21:13:49.201+08:00  INFO 16036 --- [voice-service] [restartedMain] o.apache.catalina.core.StandardEngine    : Starting Servlet engine: [Apache Tomcat/10.1.36]
2025-07-22T21:13:49.242+08:00  INFO 16036 --- [voice-service] [restartedMain] o.a.c.c.C.[Tomcat].[localhost].[/]       : Initializing Spring embedded WebApplicationContext
2025-07-22T21:13:49.243+08:00  INFO 16036 --- [voice-service] [restartedMain] w.s.c.ServletWebServerApplicationContext : Root WebApplicationContext: initialization completed in 1662 ms
2025-07-22T21:13:49.603+08:00  INFO 16036 --- [voice-service] [restartedMain] com.example.vad.impl.SileroVadModel      : 正在加载VAD模型: E:\xiaozhi-esp32-main\Springboot(1)\target\classes\models\silero_vad.onnx
2025-07-22T21:13:49.711+08:00  INFO 16036 --- [voice-service] [restartedMain] com.example.vad.impl.SileroVadModel      : Silero VAD模型初始化成功
2025-07-22T21:13:49.713+08:00  INFO 16036 --- [voice-service] [restartedMain] com.example.service.VadService           : VAD服务初始化成功
2025-07-22T21:13:49.713+08:00  INFO 16036 --- [voice-service] [restartedMain] com.example.service.VadService           : 长时间静音检测超时时间: 10000ms
2025-07-22T21:13:49.798+08:00  INFO 16036 --- [voice-service] [restartedMain] o.s.b.a.w.s.WelcomePageHandlerMapping    : Adding welcome page: class path resource [static/index.html]
2025-07-22T21:13:50.321+08:00  WARN 16036 --- [voice-service] [restartedMain] o.s.b.d.a.OptionalLiveReloadServer       : Unable to start LiveReload server
2025-07-22T21:13:50.331+08:00  INFO 16036 --- [voice-service] [restartedMain] o.s.b.a.e.web.EndpointLinksResolver      : Exposing 1 endpoint beneath base path '/actuator'
2025-07-22T21:13:50.377+08:00  WARN 16036 --- [voice-service] [restartedMain] ConfigServletWebServerApplicationContext : Exception encountered during context initialization - cancelling refresh attempt: org.springframework.context.ApplicationContextException: Failed to start bean 'webServerStartStop'
2025-07-22T21:13:50.380+08:00  INFO 16036 --- [voice-service] [restartedMain] com.example.service.VadService           : VAD服务资源已释放
2025-07-22T21:13:50.384+08:00  INFO 16036 --- [voice-service] [restartedMain] com.example.vad.impl.SileroVadModel      : Silero VAD模型资源已释放
2025-07-22T21:13:50.385+08:00  INFO 16036 --- [voice-service] [restartedMain] com.example.utils.OpusProcessor          : OpusProcessor资源已释放
2025-07-22T21:13:50.399+08:00  INFO 16036 --- [voice-service] [restartedMain] .s.b.a.l.ConditionEvaluationReportLogger : 

Error starting ApplicationContext. To display the condition evaluation report re-run your application with 'debug' enabled.
2025-07-22T21:13:50.417+08:00 ERROR 16036 --- [voice-service] [restartedMain] o.s.b.d.LoggingFailureAnalysisReporter   : 

***************************
APPLICATION FAILED TO START
***************************

Description:

Web server failed to start. Port 8080 was already in use.

Action:

Identify and stop the process that's listening on port 8080 or configure this application to listen on another port.

2025-07-22T21:14:04.613+08:00  INFO 30420 --- [voice-service] [SpringApplicationShutdownHook] o.s.b.w.e.tomcat.GracefulShutdown        : Commencing graceful shutdown. Waiting for active requests to complete
2025-07-22T21:14:04.622+08:00  INFO 30420 --- [voice-service] [tomcat-shutdown] o.s.b.w.e.tomcat.GracefulShutdown        : Graceful shutdown complete
2025-07-22T21:14:04.627+08:00  INFO 30420 --- [voice-service] [SpringApplicationShutdownHook] com.example.service.VadService           : VAD服务资源已释放
2025-07-22T21:14:04.630+08:00  INFO 30420 --- [voice-service] [SpringApplicationShutdownHook] com.example.vad.impl.SileroVadModel      : Silero VAD模型资源已释放
2025-07-22T21:14:04.631+08:00  INFO 30420 --- [voice-service] [SpringApplicationShutdownHook] com.example.utils.OpusProcessor          : OpusProcessor资源已释放
2025-07-22T21:14:10.871+08:00  INFO 30376 --- [voice-service] [restartedMain] com.example.Main                         : Starting Main using Java 23 with PID 30376 (E:\xiaozhi-esp32-main\Springboot(1)\target\classes started by 31947 in E:\xiaozhi-esp32-main\Springboot(1))
2025-07-22T21:14:10.872+08:00 DEBUG 30376 --- [voice-service] [restartedMain] com.example.Main                         : Running with Spring Boot v3.4.3, Spring v6.2.3
2025-07-22T21:14:10.873+08:00  INFO 30376 --- [voice-service] [restartedMain] com.example.Main                         : No active profile set, falling back to 1 default profile: "default"
2025-07-22T21:14:10.917+08:00  INFO 30376 --- [voice-service] [restartedMain] .e.DevToolsPropertyDefaultsPostProcessor : Devtools property defaults active! Set 'spring.devtools.add-properties' to 'false' to disable
2025-07-22T21:14:10.918+08:00  INFO 30376 --- [voice-service] [restartedMain] .e.DevToolsPropertyDefaultsPostProcessor : For additional web related logging consider setting the 'logging.level.web' property to 'DEBUG'
2025-07-22T21:14:11.991+08:00  INFO 30376 --- [voice-service] [restartedMain] o.s.b.w.embedded.tomcat.TomcatWebServer  : Tomcat initialized with port 8080 (http)
2025-07-22T21:14:12.003+08:00  INFO 30376 --- [voice-service] [restartedMain] o.apache.catalina.core.StandardService   : Starting service [Tomcat]
2025-07-22T21:14:12.004+08:00  INFO 30376 --- [voice-service] [restartedMain] o.apache.catalina.core.StandardEngine    : Starting Servlet engine: [Apache Tomcat/10.1.36]
2025-07-22T21:14:12.042+08:00  INFO 30376 --- [voice-service] [restartedMain] o.a.c.c.C.[Tomcat].[localhost].[/]       : Initializing Spring embedded WebApplicationContext
2025-07-22T21:14:12.042+08:00  INFO 30376 --- [voice-service] [restartedMain] w.s.c.ServletWebServerApplicationContext : Root WebApplicationContext: initialization completed in 1124 ms
2025-07-22T21:14:12.382+08:00  INFO 30376 --- [voice-service] [restartedMain] com.example.vad.impl.SileroVadModel      : 正在加载VAD模型: E:\xiaozhi-esp32-main\Springboot(1)\target\classes\models\silero_vad.onnx
2025-07-22T21:14:12.500+08:00  INFO 30376 --- [voice-service] [restartedMain] com.example.vad.impl.SileroVadModel      : Silero VAD模型初始化成功
2025-07-22T21:14:12.501+08:00  INFO 30376 --- [voice-service] [restartedMain] com.example.service.VadService           : VAD服务初始化成功
2025-07-22T21:14:12.502+08:00  INFO 30376 --- [voice-service] [restartedMain] com.example.service.VadService           : 长时间静音检测超时时间: 10000ms
2025-07-22T21:14:12.596+08:00  INFO 30376 --- [voice-service] [restartedMain] o.s.b.a.w.s.WelcomePageHandlerMapping    : Adding welcome page: class path resource [static/index.html]
2025-07-22T21:14:13.056+08:00  INFO 30376 --- [voice-service] [restartedMain] o.s.b.d.a.OptionalLiveReloadServer       : LiveReload server is running on port 35729
2025-07-22T21:14:13.065+08:00  INFO 30376 --- [voice-service] [restartedMain] o.s.b.a.e.web.EndpointLinksResolver      : Exposing 1 endpoint beneath base path '/actuator'
2025-07-22T21:14:13.120+08:00  INFO 30376 --- [voice-service] [restartedMain] o.s.b.w.embedded.tomcat.TomcatWebServer  : Tomcat started on port 8080 (http) with context path '/'
2025-07-22T21:14:13.130+08:00  INFO 30376 --- [voice-service] [restartedMain] com.example.Main                         : Started Main in 2.607 seconds (process running for 3.175)
2025-07-22T21:14:13.690+08:00  INFO 30376 --- [voice-service] [RMI TCP Connection(3)-**************] o.a.c.c.C.[Tomcat].[localhost].[/]       : Initializing Spring DispatcherServlet 'dispatcherServlet'
2025-07-22T21:14:13.690+08:00  INFO 30376 --- [voice-service] [RMI TCP Connection(3)-**************] o.s.web.servlet.DispatcherServlet        : Initializing Servlet 'dispatcherServlet'
2025-07-22T21:14:13.691+08:00  INFO 30376 --- [voice-service] [RMI TCP Connection(3)-**************] o.s.web.servlet.DispatcherServlet        : Completed initialization in 1 ms
2025-07-22T21:14:22.016+08:00  INFO 30376 --- [voice-service] [File Watcher] rtingClassPathChangeChangedEventListener : Restarting due to 35 class path changes (0 additions, 34 deletions, 1 modification)
2025-07-22T21:14:22.025+08:00  INFO 30376 --- [voice-service] [Thread-8] o.s.b.w.e.tomcat.GracefulShutdown        : Commencing graceful shutdown. Waiting for active requests to complete
2025-07-22T21:14:22.034+08:00  INFO 30376 --- [voice-service] [tomcat-shutdown] o.s.b.w.e.tomcat.GracefulShutdown        : Graceful shutdown complete
2025-07-22T21:14:22.040+08:00  INFO 30376 --- [voice-service] [Thread-8] com.example.service.VadService           : VAD服务资源已释放
2025-07-22T21:14:22.044+08:00  INFO 30376 --- [voice-service] [Thread-8] com.example.vad.impl.SileroVadModel      : Silero VAD模型资源已释放
2025-07-22T21:14:22.044+08:00  INFO 30376 --- [voice-service] [Thread-8] com.example.utils.OpusProcessor          : OpusProcessor资源已释放
2025-07-22T21:14:22.131+08:00  INFO 30376 --- [voice-service] [restartedMain] com.example.Main                         : Starting Main using Java 23 with PID 30376 (E:\xiaozhi-esp32-main\Springboot(1)\target\classes started by 31947 in E:\xiaozhi-esp32-main\Springboot(1))
2025-07-22T21:14:22.131+08:00 DEBUG 30376 --- [voice-service] [restartedMain] com.example.Main                         : Running with Spring Boot v3.4.3, Spring v6.2.3
2025-07-22T21:14:22.131+08:00  INFO 30376 --- [voice-service] [restartedMain] com.example.Main                         : No active profile set, falling back to 1 default profile: "default"
2025-07-22T21:14:22.459+08:00  INFO 30376 --- [voice-service] [restartedMain] o.s.b.w.embedded.tomcat.TomcatWebServer  : Tomcat initialized with port 8080 (http)
2025-07-22T21:14:22.460+08:00  INFO 30376 --- [voice-service] [restartedMain] o.apache.catalina.core.StandardService   : Starting service [Tomcat]
2025-07-22T21:14:22.460+08:00  INFO 30376 --- [voice-service] [restartedMain] o.apache.catalina.core.StandardEngine    : Starting Servlet engine: [Apache Tomcat/10.1.36]
2025-07-22T21:14:22.478+08:00  INFO 30376 --- [voice-service] [restartedMain] o.a.c.c.C.[Tomcat].[localhost].[/]       : Initializing Spring embedded WebApplicationContext
2025-07-22T21:14:22.478+08:00  INFO 30376 --- [voice-service] [restartedMain] w.s.c.ServletWebServerApplicationContext : Root WebApplicationContext: initialization completed in 345 ms
2025-07-22T21:14:22.519+08:00  INFO 30376 --- [voice-service] [restartedMain] o.s.b.a.w.s.WelcomePageHandlerMapping    : Adding welcome page: class path resource [static/index.html]
2025-07-22T21:14:22.632+08:00  INFO 30376 --- [voice-service] [restartedMain] o.s.b.d.a.OptionalLiveReloadServer       : LiveReload server is running on port 35729
2025-07-22T21:14:22.636+08:00  INFO 30376 --- [voice-service] [restartedMain] o.s.b.a.e.web.EndpointLinksResolver      : Exposing 1 endpoint beneath base path '/actuator'
2025-07-22T21:14:22.656+08:00  INFO 30376 --- [voice-service] [restartedMain] o.s.b.w.embedded.tomcat.TomcatWebServer  : Tomcat started on port 8080 (http) with context path '/'
2025-07-22T21:14:22.663+08:00  INFO 30376 --- [voice-service] [restartedMain] com.example.Main                         : Started Main in 0.559 seconds (process running for 12.707)
2025-07-22T21:14:22.670+08:00  INFO 30376 --- [voice-service] [restartedMain] .ConditionEvaluationDeltaLoggingListener : Condition evaluation delta:


==========================
CONDITION EVALUATION DELTA
==========================


Positive matches:
-----------------

   PropertyPlaceholderAutoConfiguration#propertySourcesPlaceholderConfigurer matched:
      - @ConditionalOnMissingBean (types: org.springframework.context.support.PropertySourcesPlaceholderConfigurer; SearchStrategy: current) did not find any beans (OnBeanCondition)

   TaskExecutorConfigurations.TaskExecutorConfiguration matched:
      - @ConditionalOnMissingBean (types: java.util.concurrent.Executor; SearchStrategy: all) did not find any beans (OnBeanCondition)

   TaskExecutorConfigurations.TaskExecutorConfiguration#applicationTaskExecutor matched:
      - @ConditionalOnThreading found PLATFORM (OnThreadingCondition)


Negative matches:
-----------------

   TaskExecutorConfigurations.TaskExecutorConfiguration#applicationTaskExecutorVirtualThreads:
      Did not match:
         - @ConditionalOnThreading did not find VIRTUAL (OnThreadingCondition)

   TaskSchedulingAutoConfiguration#scheduledBeanLazyInitializationExcludeFilter:
      Did not match:
         - @ConditionalOnBean (names: org.springframework.context.annotation.internalScheduledAnnotationProcessor; SearchStrategy: all) did not find any beans named org.springframework.context.annotation.internalScheduledAnnotationProcessor (OnBeanCondition)

   TaskSchedulingConfigurations.TaskSchedulerConfiguration:
      Did not match:
         - @ConditionalOnBean (names: org.springframework.context.annotation.internalScheduledAnnotationProcessor; SearchStrategy: all) did not find any beans named org.springframework.context.annotation.internalScheduledAnnotationProcessor (OnBeanCondition)


Exclusions:
-----------

    None


Unconditional classes:
----------------------

    None



2025-07-22T21:14:22.751+08:00  INFO 30376 --- [voice-service] [http-nio-0.0.0.0-8080-exec-1] o.a.c.c.C.[Tomcat].[localhost].[/]       : Initializing Spring DispatcherServlet 'dispatcherServlet'
2025-07-22T21:14:22.752+08:00  INFO 30376 --- [voice-service] [http-nio-0.0.0.0-8080-exec-1] o.s.web.servlet.DispatcherServlet        : Initializing Servlet 'dispatcherServlet'
2025-07-22T21:14:22.753+08:00  INFO 30376 --- [voice-service] [http-nio-0.0.0.0-8080-exec-1] o.s.web.servlet.DispatcherServlet        : Completed initialization in 1 ms
2025-07-22T21:14:24.471+08:00  INFO 30376 --- [voice-service] [File Watcher] rtingClassPathChangeChangedEventListener : Restarting due to 34 class path changes (34 additions, 0 deletions, 0 modifications)
2025-07-22T21:14:24.472+08:00  INFO 30376 --- [voice-service] [Thread-10] o.s.b.w.e.tomcat.GracefulShutdown        : Commencing graceful shutdown. Waiting for active requests to complete
2025-07-22T21:14:24.478+08:00  INFO 30376 --- [voice-service] [tomcat-shutdown] o.s.b.w.e.tomcat.GracefulShutdown        : Graceful shutdown complete
2025-07-22T21:14:24.529+08:00  INFO 30376 --- [voice-service] [restartedMain] com.example.Main                         : Starting Main using Java 23 with PID 30376 (E:\xiaozhi-esp32-main\Springboot(1)\target\classes started by 31947 in E:\xiaozhi-esp32-main\Springboot(1))
2025-07-22T21:14:24.529+08:00 DEBUG 30376 --- [voice-service] [restartedMain] com.example.Main                         : Running with Spring Boot v3.4.3, Spring v6.2.3
2025-07-22T21:14:24.529+08:00  INFO 30376 --- [voice-service] [restartedMain] com.example.Main                         : No active profile set, falling back to 1 default profile: "default"
2025-07-22T21:14:24.832+08:00  INFO 30376 --- [voice-service] [restartedMain] o.s.b.w.embedded.tomcat.TomcatWebServer  : Tomcat initialized with port 8080 (http)
2025-07-22T21:14:24.832+08:00  INFO 30376 --- [voice-service] [restartedMain] o.apache.catalina.core.StandardService   : Starting service [Tomcat]
2025-07-22T21:14:24.832+08:00  INFO 30376 --- [voice-service] [restartedMain] o.apache.catalina.core.StandardEngine    : Starting Servlet engine: [Apache Tomcat/10.1.36]
2025-07-22T21:14:24.854+08:00  INFO 30376 --- [voice-service] [restartedMain] o.a.c.c.C.[Tomcat].[localhost].[/]       : Initializing Spring embedded WebApplicationContext
2025-07-22T21:14:24.854+08:00  INFO 30376 --- [voice-service] [restartedMain] w.s.c.ServletWebServerApplicationContext : Root WebApplicationContext: initialization completed in 324 ms
2025-07-22T21:14:24.903+08:00  INFO 30376 --- [voice-service] [restartedMain] com.example.vad.impl.SileroVadModel      : 正在加载VAD模型: E:\xiaozhi-esp32-main\Springboot(1)\target\classes\models\silero_vad.onnx
2025-07-22T21:14:25.021+08:00  INFO 30376 --- [voice-service] [restartedMain] com.example.vad.impl.SileroVadModel      : Silero VAD模型初始化成功
2025-07-22T21:14:25.023+08:00  INFO 30376 --- [voice-service] [restartedMain] com.example.service.VadService           : VAD服务初始化成功
2025-07-22T21:14:25.024+08:00  INFO 30376 --- [voice-service] [restartedMain] com.example.service.VadService           : 长时间静音检测超时时间: 10000ms
2025-07-22T21:14:25.084+08:00  INFO 30376 --- [voice-service] [restartedMain] o.s.b.a.w.s.WelcomePageHandlerMapping    : Adding welcome page: class path resource [static/index.html]
2025-07-22T21:14:25.219+08:00  INFO 30376 --- [voice-service] [restartedMain] o.s.b.d.a.OptionalLiveReloadServer       : LiveReload server is running on port 35729
2025-07-22T21:14:25.222+08:00  INFO 30376 --- [voice-service] [restartedMain] o.s.b.a.e.web.EndpointLinksResolver      : Exposing 1 endpoint beneath base path '/actuator'
2025-07-22T21:14:25.239+08:00  INFO 30376 --- [voice-service] [restartedMain] o.s.b.w.embedded.tomcat.TomcatWebServer  : Tomcat started on port 8080 (http) with context path '/'
2025-07-22T21:14:25.246+08:00  INFO 30376 --- [voice-service] [restartedMain] com.example.Main                         : Started Main in 0.736 seconds (process running for 15.29)
2025-07-22T21:14:25.248+08:00  INFO 30376 --- [voice-service] [restartedMain] .ConditionEvaluationDeltaLoggingListener : Condition evaluation delta:


==========================
CONDITION EVALUATION DELTA
==========================


Positive matches:
-----------------

   TaskSchedulingAutoConfiguration#scheduledBeanLazyInitializationExcludeFilter matched:
      - @ConditionalOnBean (names: org.springframework.context.annotation.internalScheduledAnnotationProcessor; SearchStrategy: all) found bean 'org.springframework.context.annotation.internalScheduledAnnotationProcessor' (OnBeanCondition)

   TaskSchedulingConfigurations.TaskSchedulerConfiguration matched:
      - @ConditionalOnBean (names: org.springframework.context.annotation.internalScheduledAnnotationProcessor; SearchStrategy: all) found bean 'org.springframework.context.annotation.internalScheduledAnnotationProcessor'; @ConditionalOnMissingBean (types: org.springframework.scheduling.TaskScheduler,java.util.concurrent.ScheduledExecutorService; SearchStrategy: all) did not find any beans (OnBeanCondition)

   TaskSchedulingConfigurations.TaskSchedulerConfiguration#taskScheduler matched:
      - @ConditionalOnThreading found PLATFORM (OnThreadingCondition)


Negative matches:
-----------------

   PropertyPlaceholderAutoConfiguration#propertySourcesPlaceholderConfigurer:
      Did not match:
         - @ConditionalOnMissingBean (types: org.springframework.context.support.PropertySourcesPlaceholderConfigurer; SearchStrategy: current) found beans of type 'org.springframework.context.support.PropertySourcesPlaceholderConfigurer' propertySourcesPlaceholderConfigurer (OnBeanCondition)

   TaskExecutorConfigurations.TaskExecutorConfiguration:
      Did not match:
         - @ConditionalOnMissingBean (types: java.util.concurrent.Executor; SearchStrategy: all) found beans of type 'java.util.concurrent.Executor' taskExecutor (OnBeanCondition)

   TaskSchedulingConfigurations.TaskSchedulerConfiguration#taskSchedulerVirtualThreads:
      Did not match:
         - @ConditionalOnThreading did not find VIRTUAL (OnThreadingCondition)


Exclusions:
-----------

    None


Unconditional classes:
----------------------

    None



2025-07-22T21:14:32.390+08:00  INFO 30376 --- [voice-service] [http-nio-0.0.0.0-8080-exec-1] o.a.c.c.C.[Tomcat].[localhost].[/]       : Initializing Spring DispatcherServlet 'dispatcherServlet'
2025-07-22T21:14:32.390+08:00  INFO 30376 --- [voice-service] [http-nio-0.0.0.0-8080-exec-1] o.s.web.servlet.DispatcherServlet        : Initializing Servlet 'dispatcherServlet'
2025-07-22T21:14:32.391+08:00  INFO 30376 --- [voice-service] [http-nio-0.0.0.0-8080-exec-1] o.s.web.servlet.DispatcherServlet        : Completed initialization in 1 ms
2025-07-22T21:24:58.924+08:00  INFO 30376 --- [voice-service] [File Watcher] rtingClassPathChangeChangedEventListener : Restarting due to 1 class path change (0 additions, 1 deletion, 0 modifications)
2025-07-22T21:24:58.924+08:00  INFO 30376 --- [voice-service] [Thread-14] o.s.b.w.e.tomcat.GracefulShutdown        : Commencing graceful shutdown. Waiting for active requests to complete
2025-07-22T21:24:58.924+08:00  INFO 30376 --- [voice-service] [tomcat-shutdown] o.s.b.w.e.tomcat.GracefulShutdown        : Graceful shutdown complete
2025-07-22T21:24:58.937+08:00  INFO 30376 --- [voice-service] [Thread-14] com.example.service.VadService           : VAD服务资源已释放
2025-07-22T21:24:58.943+08:00  INFO 30376 --- [voice-service] [Thread-14] com.example.vad.impl.SileroVadModel      : Silero VAD模型资源已释放
2025-07-22T21:24:58.943+08:00  INFO 30376 --- [voice-service] [Thread-14] com.example.utils.OpusProcessor          : OpusProcessor资源已释放
2025-07-22T21:24:59.025+08:00  INFO 30376 --- [voice-service] [restartedMain] com.example.Main                         : Starting Main using Java 23 with PID 30376 (E:\xiaozhi-esp32-main\Springboot(1)\target\classes started by 31947 in E:\xiaozhi-esp32-main\Springboot(1))
2025-07-22T21:24:59.025+08:00 DEBUG 30376 --- [voice-service] [restartedMain] com.example.Main                         : Running with Spring Boot v3.4.3, Spring v6.2.3
2025-07-22T21:24:59.025+08:00  INFO 30376 --- [voice-service] [restartedMain] com.example.Main                         : No active profile set, falling back to 1 default profile: "default"
2025-07-22T21:24:59.422+08:00  INFO 30376 --- [voice-service] [restartedMain] o.s.b.w.embedded.tomcat.TomcatWebServer  : Tomcat initialized with port 8080 (http)
2025-07-22T21:24:59.422+08:00  INFO 30376 --- [voice-service] [restartedMain] o.apache.catalina.core.StandardService   : Starting service [Tomcat]
2025-07-22T21:24:59.422+08:00  INFO 30376 --- [voice-service] [restartedMain] o.apache.catalina.core.StandardEngine    : Starting Servlet engine: [Apache Tomcat/10.1.36]
2025-07-22T21:24:59.443+08:00  INFO 30376 --- [voice-service] [restartedMain] o.a.c.c.C.[Tomcat].[localhost].[/]       : Initializing Spring embedded WebApplicationContext
2025-07-22T21:24:59.443+08:00  INFO 30376 --- [voice-service] [restartedMain] w.s.c.ServletWebServerApplicationContext : Root WebApplicationContext: initialization completed in 418 ms
2025-07-22T21:24:59.484+08:00  WARN 30376 --- [voice-service] [restartedMain] ConfigServletWebServerApplicationContext : Exception encountered during context initialization - cancelling refresh attempt: org.springframework.beans.factory.UnsatisfiedDependencyException: Error creating bean with name 'webSocketConfig' defined in file [E:\xiaozhi-esp32-main\Springboot(1)\target\classes\com\example\config\WebSocketConfig.class]: Unsatisfied dependency expressed through constructor parameter 1: Error creating bean with name 'deviceWebSocketHandler': Resolution of declared constructors on bean Class [com.example.service.DeviceWebSocketHandler] from ClassLoader [org.springframework.boot.devtools.restart.classloader.RestartClassLoader@5d2ad6c3] failed
2025-07-22T21:24:59.484+08:00  INFO 30376 --- [voice-service] [restartedMain] o.apache.catalina.core.StandardService   : Stopping service [Tomcat]
2025-07-22T21:24:59.496+08:00  INFO 30376 --- [voice-service] [restartedMain] .s.b.a.l.ConditionEvaluationReportLogger : 

Error starting ApplicationContext. To display the condition evaluation report re-run your application with 'debug' enabled.
2025-07-22T21:24:59.504+08:00 ERROR 30376 --- [voice-service] [restartedMain] o.s.boot.SpringApplication               : Application run failed

org.springframework.beans.factory.UnsatisfiedDependencyException: Error creating bean with name 'webSocketConfig' defined in file [E:\xiaozhi-esp32-main\Springboot(1)\target\classes\com\example\config\WebSocketConfig.class]: Unsatisfied dependency expressed through constructor parameter 1: Error creating bean with name 'deviceWebSocketHandler': Resolution of declared constructors on bean Class [com.example.service.DeviceWebSocketHandler] from ClassLoader [org.springframework.boot.devtools.restart.classloader.RestartClassLoader@5d2ad6c3] failed
	at org.springframework.beans.factory.support.ConstructorResolver.createArgumentArray(ConstructorResolver.java:804) ~[spring-beans-6.2.3.jar:6.2.3]
	at org.springframework.beans.factory.support.ConstructorResolver.autowireConstructor(ConstructorResolver.java:240) ~[spring-beans-6.2.3.jar:6.2.3]
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.autowireConstructor(AbstractAutowireCapableBeanFactory.java:1381) ~[spring-beans-6.2.3.jar:6.2.3]
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.createBeanInstance(AbstractAutowireCapableBeanFactory.java:1218) ~[spring-beans-6.2.3.jar:6.2.3]
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.doCreateBean(AbstractAutowireCapableBeanFactory.java:563) ~[spring-beans-6.2.3.jar:6.2.3]
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.createBean(AbstractAutowireCapableBeanFactory.java:523) ~[spring-beans-6.2.3.jar:6.2.3]
	at org.springframework.beans.factory.support.AbstractBeanFactory.lambda$doGetBean$0(AbstractBeanFactory.java:339) ~[spring-beans-6.2.3.jar:6.2.3]
	at org.springframework.beans.factory.support.DefaultSingletonBeanRegistry.getSingleton(DefaultSingletonBeanRegistry.java:346) ~[spring-beans-6.2.3.jar:6.2.3]
	at org.springframework.beans.factory.support.AbstractBeanFactory.doGetBean(AbstractBeanFactory.java:337) ~[spring-beans-6.2.3.jar:6.2.3]
	at org.springframework.beans.factory.support.AbstractBeanFactory.getBean(AbstractBeanFactory.java:202) ~[spring-beans-6.2.3.jar:6.2.3]
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.instantiateSingleton(DefaultListableBeanFactory.java:1155) ~[spring-beans-6.2.3.jar:6.2.3]
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.preInstantiateSingleton(DefaultListableBeanFactory.java:1121) ~[spring-beans-6.2.3.jar:6.2.3]
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.preInstantiateSingletons(DefaultListableBeanFactory.java:1056) ~[spring-beans-6.2.3.jar:6.2.3]
	at org.springframework.context.support.AbstractApplicationContext.finishBeanFactoryInitialization(AbstractApplicationContext.java:987) ~[spring-context-6.2.3.jar:6.2.3]
	at org.springframework.context.support.AbstractApplicationContext.refresh(AbstractApplicationContext.java:627) ~[spring-context-6.2.3.jar:6.2.3]
	at org.springframework.boot.web.servlet.context.ServletWebServerApplicationContext.refresh(ServletWebServerApplicationContext.java:146) ~[spring-boot-3.4.3.jar:3.4.3]
	at org.springframework.boot.SpringApplication.refresh(SpringApplication.java:752) ~[spring-boot-3.4.3.jar:3.4.3]
	at org.springframework.boot.SpringApplication.refreshContext(SpringApplication.java:439) ~[spring-boot-3.4.3.jar:3.4.3]
	at org.springframework.boot.SpringApplication.run(SpringApplication.java:318) ~[spring-boot-3.4.3.jar:3.4.3]
	at org.springframework.boot.SpringApplication.run(SpringApplication.java:1361) ~[spring-boot-3.4.3.jar:3.4.3]
	at org.springframework.boot.SpringApplication.run(SpringApplication.java:1350) ~[spring-boot-3.4.3.jar:3.4.3]
	at com.example.Main.main(Main.java:9) ~[classes/:na]
	at java.base/jdk.internal.reflect.DirectMethodHandleAccessor.invoke(DirectMethodHandleAccessor.java:103) ~[na:na]
	at java.base/java.lang.reflect.Method.invoke(Method.java:580) ~[na:na]
	at org.springframework.boot.devtools.restart.RestartLauncher.run(RestartLauncher.java:50) ~[spring-boot-devtools-3.4.3.jar:3.4.3]
Caused by: org.springframework.beans.factory.BeanCreationException: Error creating bean with name 'deviceWebSocketHandler': Resolution of declared constructors on bean Class [com.example.service.DeviceWebSocketHandler] from ClassLoader [org.springframework.boot.devtools.restart.classloader.RestartClassLoader@5d2ad6c3] failed
	at org.springframework.beans.factory.annotation.AutowiredAnnotationBeanPostProcessor.determineCandidateConstructors(AutowiredAnnotationBeanPostProcessor.java:384) ~[spring-beans-6.2.3.jar:6.2.3]
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.determineConstructorsFromBeanPostProcessors(AbstractAutowireCapableBeanFactory.java:1320) ~[spring-beans-6.2.3.jar:6.2.3]
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.createBeanInstance(AbstractAutowireCapableBeanFactory.java:1215) ~[spring-beans-6.2.3.jar:6.2.3]
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.doCreateBean(AbstractAutowireCapableBeanFactory.java:563) ~[spring-beans-6.2.3.jar:6.2.3]
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.createBean(AbstractAutowireCapableBeanFactory.java:523) ~[spring-beans-6.2.3.jar:6.2.3]
	at org.springframework.beans.factory.support.AbstractBeanFactory.lambda$doGetBean$0(AbstractBeanFactory.java:339) ~[spring-beans-6.2.3.jar:6.2.3]
	at org.springframework.beans.factory.support.DefaultSingletonBeanRegistry.getSingleton(DefaultSingletonBeanRegistry.java:346) ~[spring-beans-6.2.3.jar:6.2.3]
	at org.springframework.beans.factory.support.AbstractBeanFactory.doGetBean(AbstractBeanFactory.java:337) ~[spring-beans-6.2.3.jar:6.2.3]
	at org.springframework.beans.factory.support.AbstractBeanFactory.getBean(AbstractBeanFactory.java:202) ~[spring-beans-6.2.3.jar:6.2.3]
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.doResolveDependency(DefaultListableBeanFactory.java:1606) ~[spring-beans-6.2.3.jar:6.2.3]
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.resolveDependency(DefaultListableBeanFactory.java:1552) ~[spring-beans-6.2.3.jar:6.2.3]
	at org.springframework.beans.factory.support.ConstructorResolver.resolveAutowiredArgument(ConstructorResolver.java:913) ~[spring-beans-6.2.3.jar:6.2.3]
	at org.springframework.beans.factory.support.ConstructorResolver.createArgumentArray(ConstructorResolver.java:791) ~[spring-beans-6.2.3.jar:6.2.3]
	... 24 common frames omitted
Caused by: java.lang.NoClassDefFoundError: com/example/service/WakeWordConfigService
	at java.base/java.lang.Class.getDeclaredConstructors0(Native Method) ~[na:na]
	at java.base/java.lang.Class.privateGetDeclaredConstructors(Class.java:3621) ~[na:na]
	at java.base/java.lang.Class.getDeclaredConstructors(Class.java:2786) ~[na:na]
	at org.springframework.beans.factory.annotation.AutowiredAnnotationBeanPostProcessor.determineCandidateConstructors(AutowiredAnnotationBeanPostProcessor.java:379) ~[spring-beans-6.2.3.jar:6.2.3]
	... 36 common frames omitted
Caused by: java.lang.ClassNotFoundException: com.example.service.WakeWordConfigService
	at java.base/jdk.internal.loader.BuiltinClassLoader.loadClass(BuiltinClassLoader.java:641) ~[na:na]
	at java.base/jdk.internal.loader.ClassLoaders$AppClassLoader.loadClass(ClassLoaders.java:188) ~[na:na]
	at java.base/java.lang.ClassLoader.loadClass(ClassLoader.java:528) ~[na:na]
	at java.base/java.lang.Class.forName0(Native Method) ~[na:na]
	at java.base/java.lang.Class.forName(Class.java:578) ~[na:na]
	at java.base/java.lang.Class.forName(Class.java:557) ~[na:na]
	at org.springframework.boot.devtools.restart.classloader.RestartClassLoader.loadClass(RestartClassLoader.java:121) ~[spring-boot-devtools-3.4.3.jar:3.4.3]
	at java.base/java.lang.ClassLoader.loadClass(ClassLoader.java:528) ~[na:na]
	... 40 common frames omitted

