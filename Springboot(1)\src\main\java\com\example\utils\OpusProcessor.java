package com.example.utils;

import io.github.jaredmdobson.concentus.*;
import jakarta.annotation.PreDestroy;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Component;

import java.io.*;
import java.nio.ByteBuffer;
import java.nio.ByteOrder;
import java.nio.ShortBuffer;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.concurrent.ConcurrentHashMap;

/**
 * Opus音频处理器
 * 负责Opus格式音频的编解码
 */
@Component
public class OpusProcessor {
    private static final Logger logger = LoggerFactory.getLogger(OpusProcessor.class);

    // 缓存
    private final ConcurrentHashMap<String, OpusDecoder> decoders = new ConcurrentHashMap<>();
    private final ConcurrentHashMap<String, OpusEncoder> encoders = new ConcurrentHashMap<>();
    private final ConcurrentHashMap<String, LeftoverState> leftoverStates = new ConcurrentHashMap<>();

    // 常量
    private static final int SAMPLE_RATE = 16000;
    private static final int CHANNELS = 1;
    private static final int FRAME_SIZE = 320; // 20ms at 16kHz
    private static final int OPUS_FRAME_DURATION_MS = 20;
    private static final int MAX_SIZE = 1275;

    // 预热帧数量
    private static final int PRE_WARM_FRAMES = 2;

    /**
     * 残留数据状态类
     */
    public static class LeftoverState {
        public short[] leftoverBuffer;
        public int leftoverCount;
        public boolean isFirst = true;

        public LeftoverState() {
            leftoverBuffer = new short[FRAME_SIZE]; // 预分配一个帧大小的缓冲区
            leftoverCount = 0;
        }

        public void clear() {
            leftoverCount = 0;
            Arrays.fill(leftoverBuffer, (short) 0);
        }
    }

    /**
     * 获取会话的残留数据状态
     */
    private LeftoverState getLeftoverState(String sid) {
        return leftoverStates.computeIfAbsent(sid, k -> new LeftoverState());
    }

    /**
     * 删除会话的残留数据状态
     */
    public void removeLeftoverState(String sid) {
        leftoverStates.remove(sid);
    }

    /**
     * Opus转PCM字节数组
     */
    public byte[] opusToPcm(String sid, byte[] data) throws OpusException {
        if (data == null || data.length == 0) {
            return new byte[0];
        }

        try {
            OpusDecoder decoder = getDecoder(sid);
            short[] buf = new short[FRAME_SIZE * 6];
            int samples = decoder.decode(data, 0, data.length, buf, 0, buf.length, false);

            byte[] pcm = new byte[samples * 2];
            for (int i = 0; i < samples; i++) {
                pcm[i * 2] = (byte) (buf[i] & 0xFF);
                pcm[i * 2 + 1] = (byte) ((buf[i] >> 8) & 0xFF);
            }

            return pcm;
        } catch (OpusException e) {
            logger.warn("解码失败: {}", e.getMessage());
            resetDecoder(sid);
            throw e;
        }
    }

    /**
     * Opus转short数组
     */
    public short[] opusToShort(String sid, byte[] data) throws OpusException {
        if (data == null || data.length == 0) {
            return new short[0];
        }

        try {
            OpusDecoder decoder = getDecoder(sid);
            short[] buf = new short[FRAME_SIZE * 6];
            int samples = decoder.decode(data, 0, data.length, buf, 0, buf.length, false);

            if (samples < buf.length) {
                short[] result = new short[samples];
                System.arraycopy(buf, 0, result, 0, samples);
                return result;
            }

            return buf;
        } catch (OpusException e) {
            logger.warn("解码失败: {}", e.getMessage());
            resetDecoder(sid);
            throw e;
        }
    }

    /**
     * PCM转Opus
     */
    public List<byte[]> pcmToOpus(String sid, byte[] pcm, boolean isStream) {
        if (pcm == null || pcm.length == 0) {
            return new ArrayList<>();
        }

        List<byte[]> frames = new ArrayList<>();
        try {
            // 获取编码器
            OpusEncoder encoder = getEncoder(sid, SAMPLE_RATE, CHANNELS);

            // 预热编码器
            byte[] opusBuf = new byte[MAX_SIZE];
            if (!isStream) {
                addPreWarmFrames(frames, encoder, FRAME_SIZE, opusBuf);
            }

            // 获取残留数据状态
            LeftoverState state = getLeftoverState(sid);

            // 将PCM字节数组转换为short数组
            short[] shortBuf = new short[pcm.length / 2];
            ByteBuffer.wrap(pcm).order(ByteOrder.LITTLE_ENDIAN).asShortBuffer().get(shortBuf);

            // 处理残留数据
            int offset = 0;
            if (state.leftoverCount > 0) {
                // 计算需要多少样本来填充残留缓冲区
                int needed = FRAME_SIZE - state.leftoverCount;
                int available = Math.min(needed, shortBuf.length);

                // 复制数据到残留缓冲区
                System.arraycopy(shortBuf, 0, state.leftoverBuffer, state.leftoverCount, available);
                state.leftoverCount += available;
                offset = available;

                // 如果残留缓冲区已满，编码并清空
                if (state.leftoverCount == FRAME_SIZE) {
                    int opusLen = encoder.encode(state.leftoverBuffer, 0, FRAME_SIZE, opusBuf, 0, opusBuf.length);
                    if (opusLen > 0) {
                        byte[] frame = new byte[opusLen];
                        System.arraycopy(opusBuf, 0, frame, 0, opusLen);
                        frames.add(frame);
                    }
                    state.clear();
                }
            }

            // 处理剩余数据
            while (offset + FRAME_SIZE <= shortBuf.length) {
                int opusLen = encoder.encode(shortBuf, offset, FRAME_SIZE, opusBuf, 0, opusBuf.length);
                if (opusLen > 0) {
                    byte[] frame = new byte[opusLen];
                    System.arraycopy(opusBuf, 0, frame, 0, opusLen);
                    frames.add(frame);
                }
                offset += FRAME_SIZE;
            }

            // 保存剩余样本到残留缓冲区
            if (offset < shortBuf.length) {
                int remaining = shortBuf.length - offset;
                System.arraycopy(shortBuf, offset, state.leftoverBuffer, 0, remaining);
                state.leftoverCount = remaining;
            }

            return frames;
        } catch (Exception e) {
            logger.error("PCM转Opus失败: {}", e.getMessage());
            return frames;
        }
    }

    /**
     * 添加预热帧
     */
    private void addPreWarmFrames(List<byte[]> frames, OpusEncoder encoder, int frameSize, byte[] opusBuf) {
        try {
            // 创建静音帧
            short[] silence = new short[frameSize];
            Arrays.fill(silence, (short) 0);

            // 添加预热帧
            for (int i = 0; i < PRE_WARM_FRAMES; i++) {
                int opusLen = encoder.encode(silence, 0, frameSize, opusBuf, 0, opusBuf.length);
                if (opusLen > 0) {
                    byte[] frame = new byte[opusLen];
                    System.arraycopy(opusBuf, 0, frame, 0, opusLen);
                    frames.add(frame);
                }
            }
        } catch (OpusException e) {
            logger.warn("添加预热帧失败: {}", e.getMessage());
        }
    }

    /**
     * 获取解码器
     */
    public OpusDecoder getDecoder(String sid) {
        return decoders.computeIfAbsent(sid, k -> {
            try {
                OpusDecoder decoder = new OpusDecoder(SAMPLE_RATE, CHANNELS);
                decoder.setGain(0);
                return decoder;
            } catch (OpusException e) {
                logger.error("创建Opus解码器失败: {}", e.getMessage());
                throw new RuntimeException("创建Opus解码器失败", e);
            }
        });
    }

    /**
     * 重置解码器
     */
    public void resetDecoder(String sid) {
        OpusDecoder decoder = decoders.remove(sid);
        if (decoder != null) {
            logger.info("重置会话 {} 的Opus解码器", sid);
        }
    }

    /**
     * 获取编码器
     */
    private OpusEncoder getEncoder(String sid, int rate, int channels) {
        return encoders.computeIfAbsent(sid, k -> {
            try {
                OpusEncoder encoder = new OpusEncoder(rate, channels, OpusApplication.OPUS_APPLICATION_VOIP);
                encoder.setBitrate(24000);
                encoder.setSignalType(OpusSignal.OPUS_SIGNAL_VOICE);
                encoder.setComplexity(8);
                encoder.setForceChannels(channels);
                encoder.setUseVBR(true);
                encoder.setUseConstrainedVBR(false);
                return encoder;
            } catch (OpusException e) {
                logger.error("创建Opus编码器失败: {}", e.getMessage());
                throw new RuntimeException("创建Opus编码器失败", e);
            }
        });
    }

    /**
     * 清理会话资源
     */
    public void cleanup(String sid) {
        OpusDecoder decoder = decoders.remove(sid);
        OpusEncoder encoder = encoders.remove(sid);
        leftoverStates.remove(sid);

        if (decoder != null || encoder != null) {
            logger.info("清理会话 {} 的Opus资源", sid);
        }
    }

    /**
     * 关闭资源
     */
    @PreDestroy
    public void cleanup() {
        decoders.clear();
        encoders.clear();
        leftoverStates.clear();
        logger.info("OpusProcessor资源已释放");
    }
} 