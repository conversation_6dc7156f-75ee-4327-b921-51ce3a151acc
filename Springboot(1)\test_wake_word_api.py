#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
小智唤醒词配置API测试脚本
用于测试唤醒词配置功能的各个API接口
"""

import requests
import json
import time

# 配置
BASE_URL = "http://localhost:8080/api/voice"
TEST_WAKE_WORDS = ["小智", "小爱", "小度", "嗨小智"]

def test_get_current_wake_word():
    """测试获取当前唤醒词配置"""
    print("🔍 测试获取当前唤醒词配置...")
    try:
        response = requests.get(f"{BASE_URL}/wake-word")
        data = response.json()
        
        if data.get("success"):
            print(f"✅ 当前唤醒词: {data.get('wake_word')}")
            return data.get('wake_word')
        else:
            print(f"❌ 获取失败: {data.get('message')}")
            return None
    except Exception as e:
        print(f"❌ 网络错误: {e}")
        return None

def test_get_supported_wake_words():
    """测试获取支持的唤醒词列表"""
    print("\n📚 测试获取支持的唤醒词列表...")
    try:
        response = requests.get(f"{BASE_URL}/wake-word/supported")
        data = response.json()
        
        if data.get("success"):
            words = data.get("supported_wake_words", [])
            print(f"✅ 支持的唤醒词 ({len(words)} 个): {', '.join(words)}")
            return words
        else:
            print(f"❌ 获取失败: {data.get('message')}")
            return []
    except Exception as e:
        print(f"❌ 网络错误: {e}")
        return []

def test_set_wake_word(wake_word):
    """测试设置唤醒词"""
    print(f"\n⚙️ 测试设置唤醒词为: {wake_word}")
    try:
        data = {"wake_word": wake_word}
        response = requests.post(f"{BASE_URL}/wake-word", data=data)
        result = response.json()
        
        if result.get("success"):
            notified = result.get("notified_devices", 0)
            print(f"✅ 设置成功! 已通知 {notified} 个设备")
            print(f"   新唤醒词: {result.get('wake_word')}")
            return True
        else:
            print(f"❌ 设置失败: {result.get('message')}")
            return False
    except Exception as e:
        print(f"❌ 网络错误: {e}")
        return False

def test_system_status():
    """测试系统状态"""
    print("\n📊 测试系统状态...")
    try:
        response = requests.get(f"{BASE_URL}/status")
        data = response.json()
        
        if data.get("status") == "running":
            print(f"✅ 系统状态: {data.get('message')}")
            return True
        else:
            print(f"❌ 系统状态异常: {data}")
            return False
    except Exception as e:
        print(f"❌ 网络错误: {e}")
        return False

def test_invalid_wake_word():
    """测试无效唤醒词"""
    print("\n🚫 测试无效唤醒词...")
    
    # 测试空唤醒词
    print("   测试空唤醒词...")
    try:
        data = {"wake_word": ""}
        response = requests.post(f"{BASE_URL}/wake-word", data=data)
        result = response.json()
        
        if not result.get("success"):
            print(f"✅ 正确拒绝空唤醒词: {result.get('message')}")
        else:
            print("❌ 应该拒绝空唤醒词但没有拒绝")
    except Exception as e:
        print(f"❌ 网络错误: {e}")
    
    # 测试过长唤醒词
    print("   测试过长唤醒词...")
    try:
        long_word = "这是一个非常非常非常长的唤醒词测试"
        data = {"wake_word": long_word}
        response = requests.post(f"{BASE_URL}/wake-word", data=data)
        result = response.json()
        
        if not result.get("success"):
            print(f"✅ 正确拒绝过长唤醒词: {result.get('message')}")
        else:
            print("❌ 应该拒绝过长唤醒词但没有拒绝")
    except Exception as e:
        print(f"❌ 网络错误: {e}")

def main():
    """主测试函数"""
    print("🎤 小智唤醒词配置API测试")
    print("=" * 50)
    
    # 测试系统状态
    if not test_system_status():
        print("❌ 系统状态检查失败，请确保Spring Boot服务正在运行")
        return
    
    # 获取当前配置
    original_wake_word = test_get_current_wake_word()
    
    # 获取支持的唤醒词列表
    supported_words = test_get_supported_wake_words()
    
    # 测试设置不同的唤醒词
    for wake_word in TEST_WAKE_WORDS:
        if test_set_wake_word(wake_word):
            time.sleep(1)  # 等待1秒
            
            # 验证设置是否生效
            current = test_get_current_wake_word()
            if current == wake_word:
                print(f"✅ 验证成功: 唤醒词已更新为 {wake_word}")
            else:
                print(f"❌ 验证失败: 期望 {wake_word}, 实际 {current}")
        
        time.sleep(2)  # 等待2秒再测试下一个
    
    # 测试无效输入
    test_invalid_wake_word()
    
    # 恢复原始唤醒词
    if original_wake_word:
        print(f"\n🔄 恢复原始唤醒词: {original_wake_word}")
        test_set_wake_word(original_wake_word)
    
    print("\n" + "=" * 50)
    print("🎉 测试完成!")
    
    # 最终状态检查
    final_wake_word = test_get_current_wake_word()
    print(f"📋 最终唤醒词配置: {final_wake_word}")

if __name__ == "__main__":
    main()
