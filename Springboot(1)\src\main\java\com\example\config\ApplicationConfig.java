package com.example.config;

import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.scheduling.annotation.EnableAsync;
import org.springframework.scheduling.concurrent.ThreadPoolTaskExecutor;
import org.springframework.web.client.RestTemplate;

import java.util.concurrent.Executor;
import java.util.concurrent.ThreadPoolExecutor;
import org.springframework.beans.factory.annotation.Value;

/**
 * 应用程序配置类
 */
@Configuration
@EnableAsync
public class ApplicationConfig {

    @Value("${thread-pool.core-size:10}")
    private int corePoolSize;

    @Value("${thread-pool.max-size:30}")
    private int maxPoolSize;

    @Value("${thread-pool.queue-capacity:100}")
    private int queueCapacity;

    @Value("${thread-pool.keep-alive-seconds:300}")
    private int keepAliveSeconds;

    /**
     * 配置RestTemplate用于HTTP请求
     */
    @Bean
    public RestTemplate restTemplate() {
        return new RestTemplate();
    }

    /**
     * 配置异步任务执行器
     */
    @Bean
    public Executor taskExecutor() {
        ThreadPoolTaskExecutor executor = new ThreadPoolTaskExecutor();
        executor.setCorePoolSize(corePoolSize);
        executor.setMaxPoolSize(maxPoolSize);
        executor.setQueueCapacity(queueCapacity);
        executor.setKeepAliveSeconds(keepAliveSeconds);
        executor.setThreadNamePrefix("voice-service-");
        
        // 设置拒绝策略
        executor.setRejectedExecutionHandler(new ThreadPoolExecutor.CallerRunsPolicy());
        executor.initialize();
        return executor;
    }

    /**
     * 配置临时文件目录
     * 
     * @return 临时目录文件对象
     */
    @Bean
    public java.io.File configureTempDirectory(@Value("${temp.dir:${java.io.tmpdir}/voice-service}") String tempDir) {
        // 设置临时文件目录
        System.setProperty("java.io.tmpdir", tempDir);
        
        // 确保目录存在
        java.io.File tempDirFile = new java.io.File(tempDir);
        if (!tempDirFile.exists()) {
            boolean created = tempDirFile.mkdirs();
            if (created) {
                System.out.println("已创建临时目录: " + tempDir);
            } else {
                System.err.println("无法创建临时目录: " + tempDir);
            }
        }
        
        return tempDirFile;
    }
    
    /**
     * 注册系统关闭钩子
     * 
     * @return 运行时对象
     */
    @Bean
    public Runtime registerShutdownHook() {
        Runtime runtime = Runtime.getRuntime();
        runtime.addShutdownHook(new Thread(() -> {
            System.out.println("应用程序正在关闭...");
            // 在这里可以添加其他清理资源的操作
        }));
        
        return runtime;
    }
} 