package com.example.service;

import org.springframework.stereotype.Service;
import java.util.*;
import java.util.concurrent.ConcurrentHashMap;
import java.io.*;
import java.nio.file.*;

/**
 * 唤醒词配置管理服务
 * 负责管理和存储唤醒词配置，支持动态更新
 */
@Service
public class WakeWordConfigService {
    
    // 配置文件路径
    private static final String CONFIG_FILE_PATH = "wake_word_config.properties";
    private static final String DEFAULT_WAKE_WORD = "小智";
    
    // 当前唤醒词配置
    private volatile String currentWakeWord = DEFAULT_WAKE_WORD;
    
    // 支持的唤醒词列表（这些是ESP-SR框架支持的唤醒词）
    private final List<String> supportedWakeWords = Arrays.asList(
        "小智",
        "小爱",
        "小度",
        "天猫精灵",
        "小米",
        "嗨小智",
        "你好小智",
        "Hi Xiaozhi",
        "Hello Xiaozhi"
    );
    
    // 设备唤醒词配置缓存 - 存储每个设备的唤醒词配置状态
    private final Map<String, String> deviceWakeWordConfig = new ConcurrentHashMap<>();
    
    public WakeWordConfigService() {
        // 启动时加载配置
        loadConfiguration();
        System.out.println("唤醒词配置服务初始化完成，当前唤醒词: " + currentWakeWord);
    }
    
    /**
     * 获取当前唤醒词
     */
    public String getCurrentWakeWord() {
        return currentWakeWord;
    }
    
    /**
     * 设置新的唤醒词
     */
    public void setWakeWord(String wakeWord) {
        if (wakeWord == null || wakeWord.trim().isEmpty()) {
            throw new IllegalArgumentException("唤醒词不能为空");
        }
        
        String trimmedWakeWord = wakeWord.trim();
        
        // 验证唤醒词是否在支持列表中
        if (!supportedWakeWords.contains(trimmedWakeWord)) {
            System.out.println("警告: 唤醒词 '" + trimmedWakeWord + "' 不在预定义支持列表中，但仍将尝试设置");
        }
        
        this.currentWakeWord = trimmedWakeWord;
        
        // 保存配置到文件
        saveConfiguration();
        
        System.out.println("唤醒词已更新为: " + currentWakeWord);
    }
    
    /**
     * 获取支持的唤醒词列表
     */
    public List<String> getSupportedWakeWords() {
        return new ArrayList<>(supportedWakeWords);
    }
    
    /**
     * 检查唤醒词是否被支持
     */
    public boolean isWakeWordSupported(String wakeWord) {
        return supportedWakeWords.contains(wakeWord);
    }
    
    /**
     * 为设备设置唤醒词配置状态
     */
    public void setDeviceWakeWordConfig(String deviceId, String wakeWord) {
        deviceWakeWordConfig.put(deviceId, wakeWord);
        System.out.println("设备 " + deviceId + " 的唤醒词配置已更新为: " + wakeWord);
    }
    
    /**
     * 获取设备的唤醒词配置
     */
    public String getDeviceWakeWordConfig(String deviceId) {
        return deviceWakeWordConfig.getOrDefault(deviceId, currentWakeWord);
    }
    
    /**
     * 移除设备的唤醒词配置
     */
    public void removeDeviceWakeWordConfig(String deviceId) {
        deviceWakeWordConfig.remove(deviceId);
        System.out.println("已移除设备 " + deviceId + " 的唤醒词配置");
    }
    
    /**
     * 获取所有设备的唤醒词配置状态
     */
    public Map<String, String> getAllDeviceConfigs() {
        return new HashMap<>(deviceWakeWordConfig);
    }
    
    /**
     * 重置所有设备配置为当前全局唤醒词
     */
    public void resetAllDeviceConfigs() {
        deviceWakeWordConfig.clear();
        System.out.println("已重置所有设备的唤醒词配置");
    }
    
    /**
     * 从文件加载配置
     */
    private void loadConfiguration() {
        try {
            Path configPath = Paths.get(CONFIG_FILE_PATH);
            if (Files.exists(configPath)) {
                Properties props = new Properties();
                try (InputStream input = Files.newInputStream(configPath)) {
                    props.load(input);
                    String savedWakeWord = props.getProperty("wake_word");
                    if (savedWakeWord != null && !savedWakeWord.trim().isEmpty()) {
                        this.currentWakeWord = savedWakeWord.trim();
                        System.out.println("从配置文件加载唤醒词: " + currentWakeWord);
                    }
                }
            } else {
                System.out.println("配置文件不存在，使用默认唤醒词: " + DEFAULT_WAKE_WORD);
                // 创建默认配置文件
                saveConfiguration();
            }
        } catch (Exception e) {
            System.err.println("加载唤醒词配置失败，使用默认配置: " + e.getMessage());
            this.currentWakeWord = DEFAULT_WAKE_WORD;
        }
    }
    
    /**
     * 保存配置到文件
     */
    private void saveConfiguration() {
        try {
            Properties props = new Properties();
            props.setProperty("wake_word", currentWakeWord);
            props.setProperty("last_updated", new Date().toString());
            
            Path configPath = Paths.get(CONFIG_FILE_PATH);
            try (OutputStream output = Files.newOutputStream(configPath)) {
                props.store(output, "Wake Word Configuration");
            }
            
            System.out.println("唤醒词配置已保存到文件: " + CONFIG_FILE_PATH);
        } catch (Exception e) {
            System.err.println("保存唤醒词配置失败: " + e.getMessage());
        }
    }
    
    /**
     * 生成唤醒词配置消息，用于发送给ESP32设备
     */
    public Map<String, Object> createWakeWordConfigMessage(String wakeWord) {
        Map<String, Object> config = new HashMap<>();
        config.put("type", "config");
        config.put("config_type", "wake_word");
        config.put("wake_word", wakeWord);
        config.put("timestamp", System.currentTimeMillis());
        
        return config;
    }
    
    /**
     * 验证唤醒词格式
     */
    public boolean isValidWakeWord(String wakeWord) {
        if (wakeWord == null || wakeWord.trim().isEmpty()) {
            return false;
        }
        
        String trimmed = wakeWord.trim();
        
        // 基本长度检查
        if (trimmed.length() < 1 || trimmed.length() > 20) {
            return false;
        }
        
        // 检查是否包含特殊字符（可根据需要调整）
        return trimmed.matches("^[\\u4e00-\\u9fa5a-zA-Z\\s]+$");
    }
    
    /**
     * 获取配置统计信息
     */
    public Map<String, Object> getConfigStats() {
        Map<String, Object> stats = new HashMap<>();
        stats.put("current_wake_word", currentWakeWord);
        stats.put("supported_wake_words_count", supportedWakeWords.size());
        stats.put("configured_devices_count", deviceWakeWordConfig.size());
        stats.put("config_file_path", CONFIG_FILE_PATH);
        
        return stats;
    }
}
