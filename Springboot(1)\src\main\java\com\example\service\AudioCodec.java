package com.example.service;

import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import jakarta.annotation.PostConstruct;
import jakarta.annotation.PreDestroy;
import java.nio.ByteBuffer;
import java.nio.ByteOrder;
import java.nio.ShortBuffer;
import java.util.Arrays;
import java.io.File;
import java.io.IOException;
import java.io.InputStream;
import java.io.FileOutputStream;

/**
 * 音频编解码处理服务
 */
@Component
public class AudioCodec {

    // 音频配置
    @Value("${audio.sample-rate:16000}")
    private int sampleRate;

    @Value("${audio.channels:1}")
    private int channels;

    @Value("${audio.frame-duration:20}")
    private int frameDurationMs;

    // 样本数据配置
    private int frameSize; // 每帧的样本数

    /**
     * 构造函数
     */
    public AudioCodec() {
        System.out.println("AudioCodec初始化");
    }
    
    /**
     * 初始化音频处理参数
     */
    @PostConstruct
    public void init() {
        // 计算每帧样本数
        frameSize = (sampleRate * frameDurationMs) / 1000;
        
        System.out.println("初始化音频处理器: 采样率=" + sampleRate + "Hz, 通道数=" + channels + 
                          ", 帧时长=" + frameDurationMs + "ms, 每帧采样=" + frameSize);
        
        // 验证帧时长参数是否与ESP32客户端兼容
        if (frameDurationMs == 60) {
            System.out.println("使用60ms帧时长，适用于ESP32设备");
        } else if (frameDurationMs != 20 && frameDurationMs != 40 && frameDurationMs != 60) {
            System.out.println("警告: 当前帧时长" + frameDurationMs + "ms可能不兼容某些ESP32设备，" +
                             "建议使用20ms, 40ms或60ms");
        }
    }

    /**
     * 获取采样率
     */
    public int getSampleRate() {
        return sampleRate;
    }
    
    /**
     * 获取声道数
     */
    public int getChannels() {
        return channels;
    }
    
    /**
     * 获取每帧采样数
     */
    public int getFrameSize() {
        return frameSize;
    }
    
    /**
     * 获取帧时长（毫秒）
     */
    public int getFrameDurationMs() {
        return frameDurationMs;
    }
    
    /**
     * 调整音频音量
     *
     * @param pcmData PCM音频数据
     * @param volume 音量系数 (1.0为原始音量，0.5为一半音量，2.0为两倍音量)
     * @return 调整音量后的PCM数据
     */
    public byte[] adjustVolume(byte[] pcmData, float volume) {
        if (pcmData == null || pcmData.length == 0) {
            return pcmData;
        }
        
        try {
            // 将字节数组转换为short数组（16位PCM）
            ShortBuffer shortBuffer = ByteBuffer.wrap(pcmData)
                    .order(ByteOrder.LITTLE_ENDIAN)
                    .asShortBuffer();
            short[] samples = new short[shortBuffer.limit()];
            shortBuffer.get(samples);
            
            // 调整音量
            for (int i = 0; i < samples.length; i++) {
                float sample = samples[i] * volume;
                // 防止溢出
                if (sample > Short.MAX_VALUE) {
                    samples[i] = Short.MAX_VALUE;
                } else if (sample < Short.MIN_VALUE) {
                    samples[i] = Short.MIN_VALUE;
                } else {
                    samples[i] = (short) sample;
                }
            }
            
            // 转换回字节数组
            ByteBuffer byteBuffer = ByteBuffer.allocate(samples.length * 2)
                    .order(ByteOrder.LITTLE_ENDIAN);
            for (short sample : samples) {
                byteBuffer.putShort(sample);
            }
            
            return byteBuffer.array();
        } catch (Exception e) {
            System.err.println("调整音量失败: " + e.getMessage());
            e.printStackTrace();
            return pcmData;
        }
    }
    
    /**
     * 生成静音音频
     *
     * @param durationMs 静音持续时间（毫秒）
     * @return 静音PCM数据
     */
    public byte[] generateSilence(int durationMs) {
        int numSamples = sampleRate * durationMs / 1000;
        int dataSize = numSamples * channels * 2; // 16位 = 2字节
        return new byte[dataSize]; // 全0就是静音
    }

    /**
     * 清理特定会话的资源
     * 
     * @param sessionId 会话ID
     */
    public void cleanup(String sessionId) {
        // 在实际实现中，这里应该清理与特定会话相关的资源
        // 例如释放与该会话关联的编解码器实例等
        System.out.println("清理会话 " + sessionId + " 的音频编解码资源");
    }
    
    /**
     * 关闭资源
     */
    @PreDestroy
    public void close() {
        System.out.println("关闭AudioCodec资源");
    }

    /**
     * 更新帧时长参数并重新计算帧大小
     * 
     * @param newFrameDurationMs 新的帧时长(毫秒)
     */
    public void updateFrameDuration(int newFrameDurationMs) {
        if (newFrameDurationMs > 0 && newFrameDurationMs != this.frameDurationMs) {
            this.frameDurationMs = newFrameDurationMs;
            this.frameSize = (sampleRate * frameDurationMs) / 1000;
            System.out.println("更新音频处理器帧时长: " + frameDurationMs + "ms, 每帧采样=" + frameSize);
            
            // 验证新设置的帧时长是否与ESP32客户端兼容
            if (newFrameDurationMs == 60) {
                System.out.println("使用60ms帧时长，适用于ESP32设备");
            } else if (newFrameDurationMs != 20 && newFrameDurationMs != 40 && newFrameDurationMs != 60) {
                System.out.println("警告: 当前帧时长" + frameDurationMs + "ms可能不兼容某些ESP32设备，" +
                                 "建议使用20ms, 40ms或60ms");
            }
        }
    }
    
    /**
     * 将Opus音频数据解码为PCM格式
     * 注意: 这是一个简化实现，实际应使用JNI调用opus库
     * 
     * @param opusData Opus编码的音频数据
     * @return PCM格式的音频数据，如果解码失败则返回空数组
     */
    public byte[] decodeToPcm(byte[] opusData) {
        if (opusData == null || opusData.length <= 2) {
            return new byte[0];
        }
        
        try {
            // 这是一个简化实现
            // 在实际项目中，应该使用JNI调用libopus库解码Opus数据
            
            // 创建模拟的PCM数据
            // 假设每个Opus帧的解码结果是frameSize个样本
            int estimatedPcmSize = frameSize * 2; // 16位PCM，每个样本2字节
            byte[] pcmData = new byte[estimatedPcmSize];
            
            // 使用Opus数据的哈希值生成一些伪随机的PCM样本，以模拟解码结果
            int hash = Arrays.hashCode(opusData);
            for (int i = 0; i < estimatedPcmSize; i += 2) {
                short sample = (short)((hash + i) % 32768);
                // 确保音量较小，避免噪音过大
                sample = (short)(sample / 64);
                
                pcmData[i] = (byte)(sample & 0xFF);
                pcmData[i+1] = (byte)((sample >> 8) & 0xFF);
            }
            
            System.out.println("已解码Opus数据 (模拟): " + opusData.length + " 字节 -> " + pcmData.length + " 字节 PCM");
            return pcmData;
        } catch (Exception e) {
            System.err.println("解码Opus数据失败: " + e.getMessage());
            e.printStackTrace();
            return new byte[0];
        }
    }
} 