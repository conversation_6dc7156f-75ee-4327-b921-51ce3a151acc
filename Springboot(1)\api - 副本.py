#!/usr/bin/env python
# -*- coding: utf-8 -*-
# Copyright (c) 2024 Alibaba Inc (authors: <PERSON><PERSON>, <PERSON>)
#
# Licensed under the Apache License, Version 2.0 (the "License");
# you may not use this file except in compliance with the License.
# You may obtain a copy of the License at
#
#   http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
# See the License for the specific language governing permissions and
# limitations under the License.

import os
import sys
import argparse
import numpy as np
import torch
import torchaudio
import librosa
import tempfile
import io
import struct
from typing import Optional
from fastapi import FastAPI, File, UploadFile, Form, HTTPException
from fastapi.responses import StreamingResponse
from fastapi.middleware.cors import CORSMiddleware
from pydantic import BaseModel
import random
import wave
import ctypes
from ctypes import *

ROOT_DIR = os.path.dirname(os.path.abspath(__file__))
sys.path.append('{}/third_party/Matcha-TTS'.format(ROOT_DIR))
from cosyvoice.cli.cosyvoice import CosyVoice, CosyVoice2
from cosyvoice.utils.file_utils import load_wav
from cosyvoice.utils.common import set_all_random_seed

app = FastAPI(title="CosyVoice API", description="API 接口服务，基于 CosyVoice 模型")

# 添加 CORS 中间件
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],  # 允许所有来源，生产环境应该设置具体的域名
    allow_credentials=True,
    allow_methods=["*"],  # 允许所有方法
    allow_headers=["*"],  # 允许所有头部
)

# 模式列表，与webui保持一致
inference_mode_list = ['预训练音色', '3s极速复刻', '跨语种复刻', '自然语言控制']
max_val = 0.8
prompt_sr = 16000


# =====================================================================
# Windows兼容的Opus编解码实现，直接使用libopus-0.dll
# =====================================================================

# 确保 libopus-0.dll在当前目录或系统路径中
OPUS_DLL_PATH = os.path.join(os.path.dirname(os.path.abspath(__file__)), "libopus-0.dll")
if not os.path.exists(OPUS_DLL_PATH):
    # 尝试多个可能的路径
    possible_paths = [
        "libopus-0.dll",  # 当前工作目录
        os.path.join("E:", "CosyVoice", "libopus-0.dll"),  # E盘CosyVoice目录
        os.path.expanduser("~\libopus-0.dll"),  # 用户目录
    ]
    
    for path in possible_paths:
        if os.path.exists(path):
            OPUS_DLL_PATH = path
            print(f"找到libopus-0.dll文件: {path}")
            break
    else:
        print("在所有路径中均未找到libopus-0.dll，将使用默认路径")
        OPUS_DLL_PATH = "libopus-0.dll"

try:
    # 加载opus库
    print(f"尝试加载opus库: {OPUS_DLL_PATH}")
    opus = ctypes.cdll.LoadLibrary(OPUS_DLL_PATH)
    print("成功加载opus库!")
except Exception as e:
    print(f"无法加载 libopus-0.dll: {e}")
    print("尝试加载替代库...")
    
    # 尝试使用替代名称的DLL
    alternative_dlls = ["opus.dll", "libopus.dll", "opusenc.dll"]
    for dll in alternative_dlls:
        try:
            print(f"尝试加载替代库: {dll}")
            opus = ctypes.cdll.LoadLibrary(dll)
            print(f"成功加载替代库: {dll}")
            break
        except Exception as e2:
            print(f"无法加载 {dll}: {e2}")
    else:
        print("所有尝试均失败，将切换到备用音频格式")
        # 不退出程序，使用备用音频格式处理
        print("将使用备用音频格式，不依赖Opus库")
        
        # 定义全局变量标记opus库是否可用
        global OPUS_AVAILABLE
        OPUS_AVAILABLE = False

# 定义Opus常量
OPUS_APPLICATION_VOIP = 2048
OPUS_APPLICATION_AUDIO = 2049
OPUS_APPLICATION_RESTRICTED_LOWDELAY = 2051

OPUS_OK = 0
OPUS_SET_BITRATE_REQUEST = 4002
OPUS_SET_COMPLEXITY_REQUEST = 4010
OPUS_SET_SIGNAL_REQUEST = 4024
OPUS_SIGNAL_VOICE = 3001
OPUS_SET_VBR_REQUEST = 4006

# 定义Opus函数原型
opus.opus_encoder_create.argtypes = [c_int, c_int, c_int, POINTER(c_int)]
opus.opus_encoder_create.restype = c_void_p

opus.opus_encode.argtypes = [c_void_p, POINTER(c_short), c_int, POINTER(c_ubyte), c_int]
opus.opus_encode.restype = c_int

opus.opus_encoder_ctl.argtypes = [c_void_p, c_int, c_int]
opus.opus_encoder_ctl.restype = c_int

opus.opus_encoder_destroy.argtypes = [c_void_p]
opus.opus_encoder_destroy.restype = None


class OpusEncoder:
    """Opus编码器封装类"""
    
    def __init__(self, sample_rate=16000, channels=1, application=OPUS_APPLICATION_VOIP,
                 bitrate=24000, complexity=10, vbr=1):
        """
        初始化Opus编码器
        
        Args:
            sample_rate: 采样率 (Hz)
            channels: 通道数
            application: 应用类型
            bitrate: 比特率 (bps)
            complexity: 复杂度 (0-10)
            vbr: 可变比特率 (0-1)
        """
        self.sample_rate = sample_rate
        self.channels = channels
        self.application = application
        
        # 创建编码器
        error = c_int()
        self.encoder = opus.opus_encoder_create(sample_rate, channels, application, byref(error))
        
        if error.value != OPUS_OK:
            raise Exception(f"创建Opus编码器失败，错误码: {error.value}")
        
        # 设置编码器参数
        opus.opus_encoder_ctl(self.encoder, OPUS_SET_BITRATE_REQUEST, bitrate)
        opus.opus_encoder_ctl(self.encoder, OPUS_SET_COMPLEXITY_REQUEST, complexity)
        opus.opus_encoder_ctl(self.encoder, OPUS_SET_SIGNAL_REQUEST, OPUS_SIGNAL_VOICE)
        opus.opus_encoder_ctl(self.encoder, OPUS_SET_VBR_REQUEST, vbr)
        
        print(f"Opus编码器初始化成功: 采样率={sample_rate}Hz, 通道数={channels}, 比特率={bitrate}bps, 复杂度={complexity}")
    
    def __del__(self):
        """销毁编码器"""
        if hasattr(self, 'encoder') and self.encoder:
            opus.opus_encoder_destroy(self.encoder)
    
    def encode_frame(self, pcm_frame, frame_size):
        """
        编码单个PCM帧
        
        Args:
            pcm_frame: PCM数据 (short数组)
            frame_size: 帧大小 (样本数)
            
        Returns:
            编码后的Opus数据
        """
        # 创建输出缓冲区 (最大1500字节)
        max_data_bytes = 1500
        opus_data = (c_ubyte * max_data_bytes)()
        
        # 确保PCM数据是short数组
        if isinstance(pcm_frame, bytes):
            # 将字节转换为short数组
            pcm_shorts = (c_short * (len(pcm_frame) // 2))()
            memmove(pcm_shorts, pcm_frame, len(pcm_frame))
            pcm_frame = pcm_shorts
        elif isinstance(pcm_frame, np.ndarray):
            # 将numpy数组转换为short数组
            pcm_shorts = (c_short * len(pcm_frame))()
            for i, sample in enumerate(pcm_frame):
                pcm_shorts[i] = int(sample)
            pcm_frame = pcm_shorts
        
        # 编码
        opus_size = opus.opus_encode(self.encoder, pcm_frame, frame_size, opus_data, max_data_bytes)
        
        if opus_size < 0:
            raise Exception(f"Opus编码失败，错误码: {opus_size}")
        
        # 返回编码后的数据
        return bytes(opus_data[:opus_size])


# 请求模型定义
class TTSRequest(BaseModel):
    tts_text: str
    mode: str
    sft_speaker: Optional[str] = None
    prompt_text: Optional[str] = None
    instruct_text: Optional[str] = None
    speed: float = 1.0


def postprocess(speech, top_db=60, hop_length=220, win_length=440):
    speech, _ = librosa.effects.trim(
        speech, top_db=top_db,
        frame_length=win_length,
        hop_length=hop_length
    )
    if speech.abs().max() > max_val:
        speech = speech / speech.abs().max() * max_val
    speech = torch.concat([speech, torch.zeros(1, int(cosyvoice.sample_rate * 0.2))], dim=1)
    return speech


def create_opus_data(audio_numpy, bitrate=24000, frame_duration=60, complexity=10, vbr=1):
    """
    将numpy数组转换为Opus格式的音频数据，严格按照参考服务端实现
    
    Args:
        audio_numpy: 音频数据的numpy数组
        bitrate: 比特率，24kbps (与参考服务端一致)
        frame_duration: 帧时长(ms)，默认60ms (ESP32兼容)
        complexity: 复杂度，默认10 (与参考服务端一致)
        vbr: 可变比特率，默认1 (开启)
        
    Returns:
        bytes: 原始Opus帧数据，不带容器格式 (ESP32客户端期望的格式)
    """
    # 确保音频在[-1, 1]范围内
    if np.abs(audio_numpy).max() > 1.0:
        audio_numpy = audio_numpy / np.abs(audio_numpy).max()
    
    # 将浮点PCM转换为16位整数PCM
    audio_int16 = (audio_numpy * 32767.0).astype(np.int16)
    
    # 创建Opus编码器
    sample_rate = 16000
    channels = 1
    application = OPUS_APPLICATION_VOIP
    
    try:
        # 创建编码器
        encoder = OpusEncoder(
            sample_rate=sample_rate, 
            channels=channels, 
            application=application,
            bitrate=bitrate,
            complexity=complexity,
            vbr=vbr
        )
        
        # 帧大小计算
        frame_size = int(sample_rate * frame_duration / 1000)  # 60ms @ 16kHz = 960 samples
        
        print(f"Opus编码: 采样率={sample_rate}Hz, 通道={channels}, 比特率={bitrate}bps, 复杂度={complexity}")
        print(f"Opus帧: 时长={frame_duration}ms, 样本数/帧={frame_size}")
        
        # 确保音频长度是frame_size的倍数
        num_frames = (len(audio_int16) + frame_size - 1) // frame_size
        padded_length = num_frames * frame_size
        if padded_length > len(audio_int16):
            padded_audio = np.zeros(padded_length, dtype=np.int16)
            padded_audio[:len(audio_int16)] = audio_int16
            audio_int16 = padded_audio
            
        print(f"PCM数据: 总样本数={len(audio_int16)}, 总帧数={num_frames}")
        
        # 创建输出缓冲区
        output = bytearray()
        total_encoded_size = 0
        
        # 逐帧编码
        for i in range(0, len(audio_int16), frame_size):
            # 获取当前帧
            frame = audio_int16[i:i + frame_size]
            
            # 确保帧长度正确
            if len(frame) < frame_size:
                # 填充静音
                padded_frame = np.zeros(frame_size, dtype=np.int16)
                padded_frame[:len(frame)] = frame
                frame = padded_frame
            
            # 编码为Opus
            encoded_frame = encoder.encode_frame(frame, frame_size)
            
            # 添加2字节的长度前缀 (与参考服务端一致)
            frame_len = len(encoded_frame)
            length_prefix = bytearray([frame_len & 0xFF, (frame_len >> 8) & 0xFF])
            
            # 添加长度前缀和帧数据
            output.extend(length_prefix)
            output.extend(encoded_frame)
            total_encoded_size += len(encoded_frame)
            
            if i == 0:
                print(f"第一帧大小: {len(encoded_frame)}字节")
        
        print(f"Opus编码完成: 总大小={total_encoded_size}字节, 平均每帧={(total_encoded_size/num_frames):.2f}字节")
        
        try:
            # 保存一份到文件以便调试
            debug_file_path = os.path.join(tempfile.gettempdir(), "debug_opus_raw.opus")
            with open(debug_file_path, "wb") as f:
                f.write(output)
            print(f"已保存调试文件: {debug_file_path}")
        except Exception as e:
            print(f"无法保存调试文件: {e}")
        
        return bytes(output)
        
    except Exception as e:
        print(f"Opus编码失败: {e}")
        # 记录详细错误
        import traceback
        traceback.print_exc()
        # 作为最后的后备方案，返回原始PCM
        return audio_int16.tobytes()


# 为兼容原始代码结构，模拟opuslib的Encoder类
class Encoder:
    """
    模拟opuslib.Encoder接口的兼容类
    """
    def __init__(self, sample_rate=16000, channels=1, application=OPUS_APPLICATION_VOIP):
        self._encoder = OpusEncoder(sample_rate, channels, application)
        self.sample_rate = sample_rate
        self.channels = channels
        self.application = application
        self._frame_size = 0
        self._bitrate = 24000
        self._complexity = 10
        self._signal = OPUS_SIGNAL_VOICE
        
    @property
    def bitrate(self):
        return self._bitrate
        
    @bitrate.setter
    def bitrate(self, value):
        self._bitrate = value
        opus.opus_encoder_ctl(self._encoder.encoder, OPUS_SET_BITRATE_REQUEST, value)
        
    @property
    def complexity(self):
        return self._complexity
        
    @complexity.setter
    def complexity(self, value):
        self._complexity = value
        opus.opus_encoder_ctl(self._encoder.encoder, OPUS_SET_COMPLEXITY_REQUEST, value)
        
    @property
    def signal(self):
        return self._signal
        
    @signal.setter
    def signal(self, value):
        self._signal = value
        opus.opus_encoder_ctl(self._encoder.encoder, OPUS_SET_SIGNAL_REQUEST, value)
        
    def encode(self, pcm_data, frame_size):
        """
        编码PCM数据
        
        Args:
            pcm_data: PCM数据 (字节)
            frame_size: 帧大小 (样本数)
            
        Returns:
            编码后的Opus数据
        """
        self._frame_size = frame_size
        return self._encoder.encode_frame(pcm_data, frame_size)


# 为保持与原代码兼容，导出这些常量
APPLICATION_VOIP = OPUS_APPLICATION_VOIP
APPLICATION_AUDIO = OPUS_APPLICATION_AUDIO
APPLICATION_RESTRICTED_LOWDELAY = OPUS_APPLICATION_RESTRICTED_LOWDELAY


# =====================================================================
# API路由实现
# =====================================================================

@app.get("/")
def read_root():
    return {"message": "CosyVoice API 服务正在运行"}


@app.get("/speakers")
async def get_speakers():
    try:
        # 获取预训练音色列表
        pretrained_speakers = cosyvoice.list_available_spks()
        if len(pretrained_speakers) == 0:
            pretrained_speakers = ["中文女", "中文男", "英文女", "英文男"]
        return {
            "speakers": pretrained_speakers
        }
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))


@app.post("/tts")
async def text_to_speech(request: TTSRequest):
    """
    文本转语音API

    - **tts_text**: 需要合成的文本
    - **mode**: 推理模式 ('预训练音色', '3s极速复刻', '跨语种复刻', '自然语言控制')
    - **sft_speaker**: 预训练音色名称（仅用于预训练音色和自然语言控制模式）
    - **prompt_text**: 示例文本（仅用于3s极速复刻模式）
    - **instruct_text**: 指令文本（仅用于自然语言控制模式）
    - **speed**: 语速调节
    """
    try:
        # 验证请求参数
        if request.mode not in inference_mode_list:
            raise HTTPException(status_code=400, detail=f"无效的模式，可用模式: {inference_mode_list}")

        # 固定随机种子为 0
        set_all_random_seed(0)

        # 根据模式进行参数验证和处理
        if request.mode == '预训练音色':
            if not request.sft_speaker or request.sft_speaker not in cosyvoice.list_available_spks():
                raise HTTPException(status_code=400, detail="请提供有效的预训练音色名称")

            # 非流式推理
            result = next(cosyvoice.inference_sft(
                request.tts_text,
                request.sft_speaker,
                stream=False,
                speed=request.speed
            ))
            audio_data = result['tts_speech'].numpy().flatten()
            
            # 生成标准的Opus格式
            opus_data = create_opus_data(audio_data)
            return StreamingResponse(
                io.BytesIO(opus_data),
                media_type="audio/opus",
                headers={
                    "Content-Disposition": "attachment; filename=audio.opus"
                }
            )

        elif request.mode == '自然语言控制':
            if cosyvoice.instruct is False:
                raise HTTPException(status_code=400,
                                    detail="当前模型不支持自然语言控制模式，请使用iic/CosyVoice-300M-Instruct模型")

            if not request.instruct_text:
                raise HTTPException(status_code=400, detail="自然语言控制模式需要提供instruct_text")

            # 非流式推理
            result = next(cosyvoice.inference_instruct(
                request.tts_text,
                request.sft_speaker,
                request.instruct_text,
                stream=False,
                speed=request.speed
            ))
            audio_data = result['tts_speech'].numpy().flatten()
            
            # 生成标准的Opus格式
            opus_data = create_opus_data(audio_data)
            return StreamingResponse(
                io.BytesIO(opus_data),
                media_type="audio/opus",
                headers={
                    "Content-Disposition": "attachment; filename=audio.opus"
                }
            )

        else:
            raise HTTPException(status_code=400, detail="此API路径不支持复刻模式，请使用/tts_clone接口")
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))


@app.post("/tts_clone")
async def clone_voice(
        tts_text: str = Form(...),
        mode: str = Form(...),
        prompt_text: Optional[str] = Form(None),
        speed: float = Form(1.0),
        prompt_audio: UploadFile = File(...)
):
    """
    复刻音色API

    - **tts_text**: 需要合成的文本
    - **mode**: 推理模式 ('3s极速复刻', '跨语种复刻')
    - **prompt_text**: 示例文本（仅用于3s极速复刻模式）
    - **speed**: 语速调节
    - **prompt_audio**: 上传的示例音频文件
    """
    # 验证请求参数
    if mode not in ['3s极速复刻', '跨语种复刻']:
        raise HTTPException(status_code=400, detail=f"无效的模式，可用模式: '3s极速复刻', '跨语种复刻'")

    # 保存上传的音频文件
    with tempfile.NamedTemporaryFile(delete=False, suffix=".wav") as temp_file:
        content = await prompt_audio.read()
        temp_file.write(content)
        temp_audio_path = temp_file.name

    try:
        # 检查音频采样率
        if torchaudio.info(temp_audio_path).sample_rate < prompt_sr:
            raise HTTPException(status_code=400,
                                detail=f'prompt音频采样率{torchaudio.info(temp_audio_path).sample_rate}低于{prompt_sr}')

        # 加载并处理音频
        prompt_speech_16k = postprocess(load_wav(temp_audio_path, prompt_sr))

        # 固定随机种子为 0
        set_all_random_seed(0)

        # 根据模式进行推理
        if mode == '3s极速复刻':
            if not prompt_text:
                raise HTTPException(status_code=400, detail="3s极速复刻模式需要提供prompt_text")
                
            result = next(cosyvoice.inference_zero_shot(
                tts_text, 
                prompt_text, 
                prompt_speech_16k, 
                stream=False,
                speed=speed
            ))
        else:  # 跨语种复刻
            result = next(cosyvoice.inference_cross_lingual(
                tts_text,
                prompt_speech_16k,
                stream=False,
                speed=speed
            ))
            
        # 获取音频数据并转换为标准Opus格式
        audio_data = result['tts_speech'].numpy().flatten()
        opus_data = create_opus_data(audio_data)
        return StreamingResponse(
            io.BytesIO(opus_data),
            media_type="audio/opus",
            headers={
                "Content-Disposition": "attachment; filename=audio.opus"
            }
        )
        
    except Exception as e:
        # 清理临时文件
        os.unlink(temp_audio_path)
        raise HTTPException(status_code=500, detail=str(e))
    finally:
        # 确保清理临时文件
        try:
            os.unlink(temp_audio_path)
        except:
            pass


if __name__ == '__main__':
    parser = argparse.ArgumentParser()
    parser.add_argument('--model_dir',
                        type=str,
       			 default='pretrained_models/CosyVoice2-0.5B',
			
                        help='local path or modelscope repo id')
    parser.add_argument('--port',
                        type=int,
                        default=8000,
                        help='server port')
    parser.add_argument('--host',
                        type=str,
                        default='0.0.0.0',
                        help='server host')
    args = parser.parse_args()

    try:
        cosyvoice = CosyVoice(args.model_dir)
    except Exception:
        try:
            cosyvoice = CosyVoice2(args.model_dir)
        except Exception:
            raise TypeError('无效的模型类型!')

    sft_spk = cosyvoice.list_available_spks()
    if len(sft_spk) == 0:
        sft_spk = ['']

    import uvicorn

    uvicorn.run(app, host=args.host, port=args.port)