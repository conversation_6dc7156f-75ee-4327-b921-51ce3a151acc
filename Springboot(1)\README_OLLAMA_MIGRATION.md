# Ollama 到 vLLM 迁移说明

## 概述

本项目已成功从 Ollama 迁移回 vLLM 作为大语言模型服务。以下是迁移的详细说明。

## 主要变更

### 1. 服务层变更

- **删除**: `OllamaService.java` - 原有的 Ollama 服务
- **新增**: `VllmService.java` - 新的 vLLM 服务

### 2. 配置文件变更

`application.properties` 中的配置已更新：

```properties
# 旧配置 (Ollama)
# ollama.api.url=http://localhost:11434/api/chat
# ollama.model.name=qwen2.5:7b

# 新配置 (vLLM)
vllm.api.url=http://localhost:8002/v1/chat/completions
vllm.model.name=Qwen3_6B
vllm.api.key=
```

### 3. 依赖注入变更

以下服务已更新为使用 `VllmService`：

- `DeviceWebSocketHandler`
- `SensevoiceService` 
- `VoiceWebSocketHandler`

## vLLM 服务特性

### API 兼容性

VllmService 提供了与原有 OllamaService 相同的接口：

- `process(String text)` - 处理文本并返回响应
- `processTextWithWebSocket(String prompt, WebSocketMessageSender messageSender)` - 流式处理
- `clearHistory()` - 清除对话历史

### 配置参数

| 参数 | 默认值 | 说明 |
|------|--------|------|
| `vllm.api.url` | `http://localhost:8002/v1/chat/completions` | vLLM API 地址 |
| `vllm.model.name` | `Qwen3_6B` | 使用的模型名称 |
| `vllm.api.key` | ` ` | API密钥（如需） |
| `vllm.api.timeout` | `60000` | API 超时时间（毫秒） |
| `vllm.max.retries` | `3` | 最大重试次数 |
| `vllm.retry.delay` | `1000` | 重试延迟（毫秒） |
| `vllm.enable.thinking` | `false` | 是否启用思考模式 |
| `vllm.system-prompt` | `你是一个智能语音助手，请简洁明了地回答问题。` | 系统提示词 |

## 部署要求

### vLLM 服务

1. 安装 vLLM: https://github.com/vllm-project/vllm
2. 拉取模型：
   ```bash
   # 下载并加载模型
   python -m vllm.entrypoints.openai.api_server \
      --model Qwen/Qwen2-7B-Instruct \
      --host 0.0.0.0 \
      --port 8002
   ```

### 验证服务

确保 vLLM 服务在 `http://localhost:8002` 上运行，可以通过以下命令测试：

```bash
curl -X POST http://localhost:8002/v1/chat/completions \
  -H "Content-Type: application/json" \
  -d '{
    "model": "Qwen/Qwen2-7B-Instruct",
    "messages": [
      {
        "role": "user",
        "content": "你好"
      }
    ]
  }'
```

## 迁移验证

### 1. 编译检查

运行以下命令确保项目编译正常：

```bash
mvn clean compile
```

### 2. 功能测试

启动应用后，测试以下功能：

- WebSocket 连接
- 语音识别
- 文本处理
- 流式响应
- 对话历史管理

### 3. 日志检查

启动时应该看到：

```
VLLM服务初始化...
API URL: http://localhost:8002/v1/chat/completions
系统提示词: 你是一个智能语音助手，请简洁明了地回答问题。
```

## 注意事项

1. **模型兼容性**: 确保使用的 vLLM 模型支持聊天格式
2. **API 格式**: vLLM 使用OpenAI兼容API格式
3. **流式处理**: 支持OpenAI兼容的SSE流式响应
4. **错误处理**: 保留了原有的重试和错误处理机制

## 回滚方案

如需回滚到 Ollama，需要：

1. 恢复 `OllamaService.java`
2. 更新配置文件中的 API 地址和模型名称
3. 更新依赖注入
4. 重新编译部署

## 性能对比

| 指标 | vLLM | Ollama |
|------|------|--------|
| 启动时间 | 较慢 | 较快 |
| 内存占用 | 较高 | 较低 |
| 响应速度 | 快 | 中等 |
| 模型支持 | 广泛 | 有限但增长快 |

## 技术支持

如遇到问题，请检查：

1. vLLM 服务是否正常运行
2. 模型是否正确加载
3. 网络连接是否正常
4. 日志中的错误信息 