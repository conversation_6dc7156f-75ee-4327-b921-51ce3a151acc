package com.example.service;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.core.io.ByteArrayResource;
import org.springframework.http.*;
import org.springframework.stereotype.Service;
import org.springframework.util.LinkedMultiValueMap;
import org.springframework.util.MultiValueMap;
import org.springframework.web.client.RestClientException;
import org.springframework.web.client.RestTemplate;
import com.example.service.VoiceWebSocketHandler.WebSocketMessageSender;

import java.io.ByteArrayInputStream;
import java.io.ByteArrayOutputStream;
import java.io.IOException;
import java.util.List;
import java.util.Map;
import java.util.HashMap;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;
import java.util.concurrent.ConcurrentHashMap;
import javax.sound.sampled.*;
import jakarta.annotation.PreDestroy;
import java.io.File;
import java.io.FileOutputStream;
import java.io.FileInputStream;
import java.util.Arrays;
import java.util.zip.GZIPInputStream;

@Service
public class SensevoiceService {

    private final RestTemplate restTemplate;
    private final VllmService vllmService;
    private final ExecutorService executorService = Executors.newCachedThreadPool();
    
    // 用于控制日志输出，每个会话一个标记
    private final Map<String, Boolean> hasLoggedWavFormat = new ConcurrentHashMap<>();

    @Value("${sensevoice.api.url:http://***********:8001/api/v1/asr}")
    private String sensevoiceApiUrl;
    
    @Value("${sensevoice.retry.max:3}")
    private int maxRetries;
    
    @Value("${sensevoice.retry.delay:1000}")
    private long retryDelayMs;

    @Autowired
    public SensevoiceService(VllmService vllmService) {
        this.restTemplate = new RestTemplate();
        this.vllmService = vllmService;
    }

    /**
     * 使用WebSocket处理语音数据并流式返回结果
     *
     * @param audioData 音频数据
     * @param messageSender WebSocket消息发送器
     */
    public void processVoiceWithWebSocket(byte[] audioData, WebSocketMessageSender messageSender) {
        executorService.submit(() -> {
            try {
                // 处理音频数据，确保格式正确
                byte[] processedAudio = ensureCorrectAudioFormat(audioData);
                
                // 发送处理状态
                Map<String, Object> statusData = new HashMap<>();
                statusData.put("message", "正在识别语音...");
                messageSender.sendWebSocketMessage("status", statusData);
                
                // 第一步：调用SenseVoice API进行语音识别，带重试
                // 直接调用API，不使用recognizeSpeech方法避免递归
                String recognizedText = recognizeSpeechWithRetry(processedAudio);
                
                if (recognizedText == null || recognizedText.trim().isEmpty()) {
                    throw new Exception("语音识别未返回任何文本");
                }
                
                // 发送识别结果到前端
                Map<String, Object> recognitionData = new HashMap<>();
                recognitionData.put("text", recognizedText);
                messageSender.sendWebSocketMessage("recognition", recognitionData);
                
                System.out.println("识别结果已发送到前端: " + recognizedText);
                
                // 发送处理状态
                Map<String, Object> processingData = new HashMap<>();
                processingData.put("message", "正在处理文本...");
                messageSender.sendWebSocketMessage("status", processingData);
                
                // 第二步：调用VLLM进行文本处理并流式返回
                try {
                    // 处理识别出的文本，确保格式正确
                    String processedText = processRecognizedText(recognizedText);
                    System.out.println("准备发送到VLLM的文本: " + processedText);
                    
                    // 使用WebSocket方式处理流式响应
                    vllmService.processTextWithWebSocket(processedText, messageSender);
                } catch (Exception e) {
                    System.err.println("VLLM处理失败，尝试非流式处理: " + e.getMessage());
                    
                    // 如果流式处理失败，尝试非流式处理
                    try {
                        String response = vllmService.process(recognizedText);
                        Map<String, Object> messageData = new HashMap<>();
                        messageData.put("text", response);
                        messageSender.sendWebSocketMessage("message", messageData);
                        messageSender.complete();
                    } catch (Exception ex) {
                        System.err.println("VLLM处理失败: " + ex.getMessage());
                        throw new Exception("AI处理失败: " + e.getMessage() + ", " + ex.getMessage());
                    }
                }
            } catch (Exception e) {
                System.err.println("处理语音失败: " + e.getMessage());
                e.printStackTrace();
                Map<String, Object> errorData = new HashMap<>();
                errorData.put("message", "处理失败: " + e.getMessage());
                try {
                    messageSender.sendWebSocketMessage("error", errorData);
                } catch (IOException ex) {
                    System.err.println("发送错误消息失败: " + ex.getMessage());
                }
            }
        });
    }
    
    /**
     * 带重试机制的语音识别
     */
    private String recognizeSpeechWithRetry(byte[] audioData) throws Exception {
        Exception lastException = null;
        
        for (int attempt = 0; attempt < maxRetries; attempt++) {
            try {
                if (attempt > 0) {
                    System.out.println("尝试语音识别第 " + (attempt + 1) + " 次...");
                    Thread.sleep(retryDelayMs * attempt);
                }
                
                // 创建请求头
                HttpHeaders headers = new HttpHeaders();
                headers.setContentType(MediaType.MULTIPART_FORM_DATA);
                
                // 创建请求体
                MultiValueMap<String, Object> body = new LinkedMultiValueMap<>();
                ByteArrayResource resource = new ByteArrayResource(audioData) {
                    @Override
                    public String getFilename() {
                        return "audio.wav";
                    }
                    
                    @Override
                    public long contentLength() {
                        return audioData.length;
                    }
                };
                
                // 修正参数名称，与API要求一致
                body.add("files", resource);
                body.add("keys", "audio");
                body.add("lang", "auto");
                
                // 创建HTTP实体
                HttpEntity<MultiValueMap<String, Object>> requestEntity = new HttpEntity<>(body, headers);
                
                // 修正API路径
                String apiUrl = sensevoiceApiUrl;
                if (!apiUrl.contains("/v1/")) {
                    apiUrl = apiUrl.replace("/api/asr", "/api/v1/asr");
                }
                
                System.out.println("发送请求到SenseVoice API: " + apiUrl + " (尝试 " + (attempt + 1) + "/" + maxRetries + ")");
                
                // 发送请求
                ResponseEntity<Map> responseEntity = restTemplate.postForEntity(
                    apiUrl,
                    requestEntity,
                    Map.class
                );
                
                // 检查响应
                if (responseEntity.getStatusCode().is2xxSuccessful()) {
                    Map<String, Object> responseBody = responseEntity.getBody();
                    if (responseBody != null) {
                        // 处理API返回的结果格式
                        if (responseBody.containsKey("result")) {
                            List<Map<String, Object>> results = (List<Map<String, Object>>) responseBody.get("result");
                            if (results != null && !results.isEmpty()) {
                                String recognizedText = (String) results.get(0).get("text");
                                System.out.println("语音识别成功: " + recognizedText);
                                return recognizedText;
                            } else {
                                throw new Exception("语音识别结果为空");
                            }
                        } else if (responseBody.containsKey("text")) {
                            // 兼容可能的直接返回text的情况
                            String recognizedText = (String) responseBody.get("text");
                            System.out.println("语音识别成功: " + recognizedText);
                            return recognizedText;
                        } else {
                            throw new Exception("语音识别响应中未包含文本: " + responseBody);
                        }
                    } else {
                        throw new Exception("语音识别响应为空");
                    }
                } else {
                    throw new Exception("语音识别请求失败，状态码: " + responseEntity.getStatusCode());
                }
            } catch (Exception e) {
                System.err.println("第 " + (attempt + 1) + " 次识别失败: " + e.getMessage());
                lastException = e;
                
                // 如果是服务器错误或网络错误，继续重试
                if (e.getMessage().contains("500") || 
                    e.getMessage().contains("502") || 
                    e.getMessage().contains("503") || 
                    e.getMessage().contains("504") ||
                    e.getMessage().contains("Connection") ||
                    e.getMessage().contains("Timeout")) {
                    continue;
                } else {
                    // 其他类型错误直接抛出
                    throw e;
                }
            }
        }
        
        // 所有重试都失败
        throw new Exception("语音识别失败，已重试 " + maxRetries + " 次: " + (lastException != null ? lastException.getMessage() : "未知错误"));
    }

    /**
     * 直接处理音频数据进行语音识别
     * 
     * @param audioData 音频数据（PCM格式）
     * @return 识别出的文本
     * @throws Exception 如果处理过程中出错
     */
    public String recognizeSpeech(byte[] audioData) throws Exception {
        return recognizeSpeech(audioData, null);
    }
    
    /**
     * 直接处理音频数据进行语音识别（带会话ID）
     * 
     * @param audioData 音频数据（PCM格式）
     * @param sessionId 会话ID，用于控制日志输出
     * @return 识别出的文本
     * @throws Exception 如果处理过程中出错
     */
    public String recognizeSpeech(byte[] audioData, String sessionId) throws Exception {
        // recognizeSpeech方法开头加日志
        System.out.println("[Sensevoice] recognizeSpeech: audioData.length=" + (audioData != null ? audioData.length : 0) + ", sessionId=" + sessionId);
        if (audioData != null && audioData.length > 0) {
            int previewLen = Math.min(16, audioData.length);
            StringBuilder sb = new StringBuilder();
            for (int i = 0; i < previewLen; i++) sb.append(String.format("%02X ", audioData[i]));
            System.out.println("[Sensevoice] audioData preview: " + sb.toString());
        }
        // 处理音频数据，确保格式正确
        byte[] processedAudio = ensureCorrectAudioFormat(audioData, sessionId);
        
        // 调用SenseVoice API进行语音识别
        try {
            // 创建请求头
            HttpHeaders headers = new HttpHeaders();
            headers.setContentType(MediaType.MULTIPART_FORM_DATA);
            
            // 创建请求体
            MultiValueMap<String, Object> body = new LinkedMultiValueMap<>();
            ByteArrayResource resource = new ByteArrayResource(processedAudio) {
                @Override
                public String getFilename() {
                    return "audio.wav";
                }
                
                @Override
                public long contentLength() {
                    return processedAudio.length;
                }
            };
            
            // 修正参数名称，与API要求一致
            body.add("files", resource);
            body.add("keys", "audio");
            body.add("lang", "auto");
            
            // 创建HTTP实体
            HttpEntity<MultiValueMap<String, Object>> requestEntity = new HttpEntity<>(body, headers);
            
            // 修正API路径
            String apiUrl = sensevoiceApiUrl;
            if (!apiUrl.contains("/v1/")) {
                apiUrl = apiUrl.replace("/api/asr", "/api/v1/asr");
            }
            
            System.out.println("发送请求到SenseVoice API: " + apiUrl);
            System.out.println("音频数据大小: " + processedAudio.length + " 字节");
            
            // 发送请求
            ResponseEntity<Map> responseEntity = restTemplate.postForEntity(
                apiUrl,
                requestEntity,
                Map.class
            );
            
            // 检查响应
            if (responseEntity.getStatusCode().is2xxSuccessful()) {
                Map<String, Object> responseBody = responseEntity.getBody();
                if (responseBody != null) {
                    // 处理API返回的结果格式
                    if (responseBody.containsKey("result")) {
                        List<Map<String, Object>> results = (List<Map<String, Object>>) responseBody.get("result");
                        if (results != null && !results.isEmpty()) {
                            String recognizedText = (String) results.get(0).get("text");
                            System.out.println("[Sensevoice] 识别结果: " + recognizedText);
                            return recognizedText;
                        } else {
                            throw new Exception("语音识别结果为空");
                        }
                    } else if (responseBody.containsKey("text")) {
                        // 兼容可能的直接返回text的情况
                        String recognizedText = (String) responseBody.get("text");
                        System.out.println("[Sensevoice] 识别结果: " + recognizedText);
                        return recognizedText;
                    } else {
                        throw new Exception("语音识别响应中未包含文本: " + responseBody);
                    }
                } else {
                    throw new Exception("语音识别响应为空");
                }
            } else {
                throw new Exception("语音识别请求失败，状态码: " + responseEntity.getStatusCode());
            }
        } catch (RestClientException e) {
            System.err.println("[Sensevoice] recognizeSpeech异常: " + e.getMessage());
            e.printStackTrace();
            throw new Exception("语音识别请求异常: " + e.getMessage(), e);
        }
    }

    /**
     * 确保音频格式正确，如果需要则进行转换
     * 转换为WAV格式，16kHz采样率，16位深度，单声道
     */
    private byte[] ensureCorrectAudioFormat(byte[] audioData) throws Exception {
        return ensureCorrectAudioFormat(audioData, null);
    }
    
    /**
     * 确保音频格式正确，如果需要则进行转换
     * 转换为WAV格式，16kHz采样率，16位深度，单声道
     * @param audioData 音频数据
     * @param sessionId 会话ID，用于控制日志输出
     */
    private byte[] ensureCorrectAudioFormat(byte[] audioData, String sessionId) throws Exception {
        System.out.println("[Sensevoice] ensureCorrectAudioFormat: audioData.length=" + (audioData != null ? audioData.length : 0) + ", sessionId=" + sessionId);
        try {
            // 先检查数据是否已经是WAV格式
            if (isWavFormat(audioData, sessionId)) {
                return audioData;
            }
            
            // 检查是否是Opus格式（ESP32发送的格式）
            if (isOpusFormat(audioData, sessionId)) {
                System.out.println("[Sensevoice] 检测到Opus格式，进行解码转换");
                return convertOpusToWav(audioData, sessionId);
            }
            
            // 如果不是WAV或Opus，尝试通过不同的方法转换
            try {
                return convertAudioUsingDirectMethod(audioData);
            } catch (Exception e) {
                System.err.println("直接转换方法失败: " + e.getMessage());
                // 尝试备用方法
                return convertAudioUsingBackupMethod(audioData);
            }
        } catch (Exception e) {
            System.err.println("音频转换失败: " + e.getMessage());
            e.printStackTrace();
            // 如果所有转换都失败，返回原始数据
            return audioData;
        }
    }

    /**
     * 检查数据是否已经是WAV格式
     * @param sessionId 可选的会话ID，用于控制日志输出
     */
    private boolean isWavFormat(byte[] data, String sessionId) {
        // WAV文件头标识 "RIFF" 和 "WAVE"
        if (data.length < 12) {
            return false;
        }
        
        boolean isWav = data[0] == 'R' && data[1] == 'I' && data[2] == 'F' && data[3] == 'F' &&
               data[8] == 'W' && data[9] == 'A' && data[10] == 'V' && data[11] == 'E';
        System.out.println("[Sensevoice] isWavFormat: " + isWav + ", data.length=" + data.length + ", sessionId=" + sessionId);
               
        // 只在每个会话第一次检测到时输出日志
        if (isWav) {
            if (sessionId != null) {
                if (!hasLoggedWavFormat.getOrDefault(sessionId, false)) {
                    System.out.println("检测到WAV格式，尝试直接使用");
                    hasLoggedWavFormat.put(sessionId, true);
                }
            } else if (!hasLoggedWavFormat.getOrDefault("default", false)) {
                // 如果没有提供会话ID，使用默认标记
                System.out.println("检测到WAV格式，尝试直接使用");
                hasLoggedWavFormat.put("default", true);
            }
        }
        
        return isWav;
    }
    
    /**
     * 重载方法，兼容无sessionId的调用
     */
    private boolean isWavFormat(byte[] data) {
        return isWavFormat(data, null);
    }
    
    /**
     * 检查数据是否是Opus格式
     */
    private boolean isOpusFormat(byte[] data, String sessionId) {
        if (data == null || data.length < 4) {
            return false;
        }
        
        // Opus格式检测：检查是否包含Opus帧特征
        // Opus帧通常以特定的TOC字节开始
        boolean isOpus = false;
        
        // 检查前几个字节是否包含常见的Opus帧头特征
        for (int i = 0; i < Math.min(data.length - 1, 100); i++) {
            byte b = data[i];
            // Opus TOC字节通常在特定范围内
            if ((b & 0xC0) == 0x40 || (b & 0xC0) == 0x80 || (b & 0xC0) == 0xC0) {
                isOpus = true;
                break;
            }
        }
        
        System.out.println("[Sensevoice] isOpusFormat: " + isOpus + ", data.length=" + data.length + ", sessionId=" + sessionId);
        return isOpus;
    }

    /**
     * 将Opus音频转换为WAV格式
     */
    private byte[] convertOpusToWav(byte[] opusData, String sessionId) throws Exception {
        System.out.println("[Sensevoice] 开始Opus到WAV转换，数据大小: " + opusData.length);
        
        // 由于Java没有内置的Opus解码器，我们需要使用外部库或系统命令
        // 这里提供一个简化的实现，假设数据已经是PCM格式但被误认为是Opus
        
        // 方法1：尝试直接作为PCM数据处理
        try {
            return convertRawPcmToWav(opusData, 16000, 1, 16);
        } catch (Exception e) {
            System.err.println("直接PCM转换失败: " + e.getMessage());
        }
        
        // 方法2：如果上面失败，尝试使用系统命令（如果可用）
        try {
            return convertOpusUsingSystemCommand(opusData);
        } catch (Exception e) {
            System.err.println("系统命令转换失败: " + e.getMessage());
        }
        
        // 方法3：最后的备用方案，假设数据是某种PCM变体
        System.out.println("[Sensevoice] 使用备用方案处理音频数据");
        return convertAudioUsingBackupMethod(opusData);
    }

    /**
     * 将原始PCM数据转换为WAV格式
     */
    private byte[] convertRawPcmToWav(byte[] pcmData, int sampleRate, int channels, int bitsPerSample) throws Exception {
        System.out.println("[Sensevoice] 转换原始PCM数据到WAV，采样率: " + sampleRate + "Hz, 通道: " + channels + ", 位深: " + bitsPerSample);
        
        int byteRate = sampleRate * channels * bitsPerSample / 8;
        int blockAlign = channels * bitsPerSample / 8;
        int dataSize = pcmData.length;
        int chunkSize = 36 + dataSize;

        ByteArrayOutputStream out = new ByteArrayOutputStream();
        try {
            // RIFF header
            out.write("RIFF".getBytes("US-ASCII"));
            out.write(new byte[] {
                (byte) (chunkSize & 0xff),
                (byte) ((chunkSize >> 8) & 0xff),
                (byte) ((chunkSize >> 16) & 0xff),
                (byte) ((chunkSize >> 24) & 0xff)
            });
            out.write("WAVE".getBytes("US-ASCII"));

            // fmt subchunk
            out.write("fmt ".getBytes("US-ASCII"));
            out.write(new byte[] {16, 0, 0, 0}); // Subchunk1Size (16 for PCM)
            out.write(new byte[] {1, 0}); // AudioFormat (1 for PCM)
            out.write(new byte[] {(byte) channels, 0}); // NumChannels
            out.write(new byte[] {
                (byte) (sampleRate & 0xff),
                (byte) ((sampleRate >> 8) & 0xff),
                (byte) ((sampleRate >> 16) & 0xff),
                (byte) ((sampleRate >> 24) & 0xff)
            }); // SampleRate
            out.write(new byte[] {
                (byte) (byteRate & 0xff),
                (byte) ((byteRate >> 8) & 0xff),
                (byte) ((byteRate >> 16) & 0xff),
                (byte) ((byteRate >> 24) & 0xff)
            }); // ByteRate
            out.write(new byte[] {
                (byte) (blockAlign & 0xff),
                (byte) ((blockAlign >> 8) & 0xff)
            }); // BlockAlign
            out.write(new byte[] {
                (byte) bitsPerSample,
                0
            }); // BitsPerSample

            // data subchunk
            out.write("data".getBytes("US-ASCII"));
            out.write(new byte[] {
                (byte) (dataSize & 0xff),
                (byte) ((dataSize >> 8) & 0xff),
                (byte) ((dataSize >> 16) & 0xff),
                (byte) ((dataSize >> 24) & 0xff)
            });
            out.write(pcmData);

            byte[] result = out.toByteArray();
            System.out.println("[Sensevoice] WAV转换完成，输出大小: " + result.length + " 字节");
            return result;
        } catch (IOException e) {
            System.err.println("PCM to WAV conversion failed: " + e.getMessage());
            throw new Exception("PCM to WAV conversion failed", e);
        }
    }

    /**
     * 使用系统命令转换Opus（如果可用）
     */
    private byte[] convertOpusUsingSystemCommand(byte[] opusData) throws Exception {
        // 检查系统是否有ffmpeg或opusdec
        try {
            // 创建临时文件
            File tempOpusFile = File.createTempFile("temp_opus_", ".opus");
            File tempWavFile = File.createTempFile("temp_wav_", ".wav");
            
            try {
                // 写入Opus数据到临时文件
                try (FileOutputStream fos = new FileOutputStream(tempOpusFile)) {
                    fos.write(opusData);
                }
                
                // 使用ffmpeg转换
                ProcessBuilder pb = new ProcessBuilder(
                    "ffmpeg", "-i", tempOpusFile.getAbsolutePath(),
                    "-ar", "16000", "-ac", "1", "-f", "wav",
                    tempWavFile.getAbsolutePath()
                );
                pb.redirectErrorStream(true);
                
                Process process = pb.start();
                int exitCode = process.waitFor();
                
                if (exitCode == 0 && tempWavFile.exists()) {
                    // 读取转换后的WAV文件
                    byte[] wavData = java.nio.file.Files.readAllBytes(tempWavFile.toPath());
                    System.out.println("[Sensevoice] 系统命令转换成功，输出大小: " + wavData.length + " 字节");
                    return wavData;
                } else {
                    throw new Exception("ffmpeg转换失败，退出码: " + exitCode);
                }
            } finally {
                // 清理临时文件
                tempOpusFile.delete();
                tempWavFile.delete();
            }
        } catch (Exception e) {
            System.err.println("系统命令转换失败: " + e.getMessage());
            throw e;
        }
    }
    
    /**
     * 清理指定会话的日志标记
     */
    public void resetSessionLogFlags(String sessionId) {
        if (sessionId != null) {
            hasLoggedWavFormat.remove(sessionId);
        }
    }
    
    /**
     * 清理所有会话的日志标记
     */
    public void resetAllLogFlags() {
        hasLoggedWavFormat.clear();
    }

    /**
     * 使用直接方法转换音频
     */
    private byte[] convertAudioUsingDirectMethod(byte[] audioData) throws Exception {
        // 假设输入是16kHz的PCM数据（ESP32的标准输出）
        AudioFormat sourceFormat = new AudioFormat(
            16000.0F, // 采样率 - ESP32标准采样率
            16,       // 采样大小
            1,        // 声道数 (单声道)
            true,     // 有符号
            false     // 小端字节序
        );
        
        // 创建目标格式：16kHz, 16-bit, mono（与源格式相同）
        AudioFormat targetFormat = new AudioFormat(
            16000.0F, // 采样率
            16,       // 采样大小
            1,        // 声道数 (单声道)
            true,     // 有符号
            false     // 小端字节序
        );
        
        // 创建定长的音频输入流，明确指定帧长度
        int frameSize = sourceFormat.getFrameSize();
        int frames = audioData.length / frameSize;
        AudioInputStream inputStream = new AudioInputStream(
            new ByteArrayInputStream(audioData), 
            sourceFormat, 
            frames
        );
        
        // 转换格式（如果源格式和目标格式相同，实际上不会进行转换）
        AudioInputStream convertedStream = AudioSystem.getAudioInputStream(targetFormat, inputStream);
        
        // 使用ByteArrayOutputStream捕获输出
        ByteArrayOutputStream out = new ByteArrayOutputStream();
        
        // 手动写入WAV头部
        writeWaveHeader(out, targetFormat, (int)convertedStream.getFrameLength());
        
        // 复制音频数据
        byte[] buffer = new byte[1024];
        int bytesRead;
        while ((bytesRead = convertedStream.read(buffer)) != -1) {
            out.write(buffer, 0, bytesRead);
        }
        
        return out.toByteArray();
    }

    /**
     * 使用备用方法转换音频
     */
    private byte[] convertAudioUsingBackupMethod(byte[] audioData) throws Exception {
        // 如果原始方法失败，尝试通过PCM转换
        // 假设输入是raw PCM数据，创建一个16kHz 16位单声道格式
        AudioFormat targetFormat = new AudioFormat(16000, 16, 1, true, false);
        
        // 计算样本数量（假设输入数据是16位）
        int sampleCount = audioData.length / 2;
        
        // 重采样到16kHz (简化版本)
        byte[] resampledData = new byte[sampleCount * 2];
        System.arraycopy(audioData, 0, resampledData, 0, Math.min(audioData.length, resampledData.length));
        
        // 使用ByteArrayOutputStream捕获输出
        ByteArrayOutputStream out = new ByteArrayOutputStream();
        
        // 手动写入WAV头部
        writeWaveHeader(out, targetFormat, sampleCount);
        
        // 写入音频数据
        out.write(resampledData);
        
        return out.toByteArray();
    }

    /**
     * 手动写入WAV文件头
     */
    private void writeWaveHeader(ByteArrayOutputStream out, AudioFormat format, int totalFrames) throws IOException {
        // RIFF header
        out.write("RIFF".getBytes());
        
        // Chunk size (file size - 8)
        int dataSize = totalFrames * format.getFrameSize();
        int fileSize = 36 + dataSize;
        writeInt(out, fileSize - 8);
        
        // WAVE header
        out.write("WAVE".getBytes());
        
        // fmt chunk
        out.write("fmt ".getBytes());
        writeInt(out, 16); // fmt chunk size
        writeShort(out, 1); // audio format (1 = PCM)
        writeShort(out, format.getChannels()); // channels
        writeInt(out, (int)format.getSampleRate()); // sample rate
        writeInt(out, (int)(format.getSampleRate() * format.getFrameSize())); // byte rate
        writeShort(out, format.getFrameSize()); // block align
        writeShort(out, format.getSampleSizeInBits()); // bits per sample
        
        // data chunk
        out.write("data".getBytes());
        writeInt(out, dataSize); // data chunk size
    }

    /**
     * 写入32位整数（小端序）
     */
    private void writeInt(ByteArrayOutputStream out, int value) throws IOException {
        out.write(value & 0xFF);
        out.write((value >> 8) & 0xFF);
        out.write((value >> 16) & 0xFF);
        out.write((value >> 24) & 0xFF);
    }

    /**
     * 写入16位整数（小端序）
     */
    private void writeShort(ByteArrayOutputStream out, int value) throws IOException {
        out.write(value & 0xFF);
        out.write((value >> 8) & 0xFF);
    }

    /**
     * 将音频数据保存到临时文件用于调试
     */
    private void saveAudioForDebug(byte[] audioData) {
//        try {
//            java.nio.file.Path tempFile = java.nio.file.Files.createTempFile("audio_debug_", ".wav");
//            java.nio.file.Files.write(tempFile, audioData);
//            System.out.println("已保存音频文件用于调试: " + tempFile.toAbsolutePath());
//        } catch (Exception e) {
//            System.err.println("保存调试音频文件失败: " + e.getMessage());
//        }
    }

    /**
     * 将字节数组转换为十六进制字符串表示形式
     */
    private String bytesToHexString(byte[] bytes, int offset, int length) {
        StringBuilder sb = new StringBuilder();
        for (int i = offset; i < offset + length && i < bytes.length; i++) {
            sb.append(String.format("%02X ", bytes[i] & 0xFF));
        }
        return sb.toString();
    }

    /**
     * 处理识别出的文本，使其更适合Ollama处理
     */
    private String processRecognizedText(String text) {
        if (text == null || text.trim().isEmpty()) {
            return "您好，请问有什么可以帮助您?";
        }
        
        // 添加适当的提示语
//        return "用户说: " + text.trim() + "\n请以自然、友好的方式回应用户，不要太正式。";
        return text.trim();
    }
    
    /**
     * 在服务关闭时释放资源
     */
    public void shutdown() {
        executorService.shutdown();
    }
} 