package com.example.controller;

import com.example.service.SensevoiceService;
import com.example.service.CosyvoiceService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import java.io.IOException;
import java.util.HashMap;
import java.util.Map;

import com.example.service.DeviceWebSocketHandler;
import com.example.service.VadService;
import com.example.service.WakeWordConfigService;

@RestController
@RequestMapping("/api/voice")
@CrossOrigin(origins = "*") // 允许跨域请求
public class VoiceController {

    private final SensevoiceService sensevoiceService;
    private final CosyvoiceService cosyvoiceService;

    @Autowired
    private DeviceWebSocketHandler deviceWebSocketHandler;
    
    @Autowired
    private VadService vadService;

    @Autowired
    private WakeWordConfigService wakeWordConfigService;

    @Autowired
    public VoiceController(SensevoiceService sensevoiceService, CosyvoiceService cosyvoiceService) {
        this.sensevoiceService = sensevoiceService;
        this.cosyvoiceService = cosyvoiceService;
    }

    // 注意: WebSocket通信通过"/api/voice/ws"端点处理，通过WebSocketConfig和VoiceWebSocketHandler配置，
    // 此控制器仅处理HTTP请求

    /**
     * 接收音频文件，返回识别后的文本
     * 
     * @param audio 音频文件
     * @return 识别后的文本
     */
    @PostMapping("/recognize")
    public ResponseEntity<String> recognizeVoice(@RequestParam("audio") MultipartFile audio) {
        try {
            // 获取音频信息
            byte[] audioData = audio.getBytes();
            String originalFilename = audio.getOriginalFilename();
            String contentType = audio.getContentType();
            
            System.out.println("接收到音频文件: " + originalFilename);
            System.out.println("文件类型: " + contentType);
            System.out.println("文件大小: " + audioData.length + " 字节");
            
            // 目前只是简单返回文本，这个方法还需保留以兼容现有代码
            return ResponseEntity.ok("语音识别接口调用成功");
        } catch (IOException e) {
            System.err.println("处理音频文件失败: " + e.getMessage());
            e.printStackTrace();
            return ResponseEntity.badRequest().body("处理音频失败: " + e.getMessage());
        }
    }
    
    /**
     * 测试TTS功能
     * 
     * @param text 要转换为语音的文本
     * @param speaker 选择的音色
     * @return 操作结果
     */
    @PostMapping("/test-tts")
    public ResponseEntity<Map<String, Object>> testTTS(
            @RequestParam("text") String text,
            @RequestParam(value = "speaker", required = false, defaultValue = "中文女") String speaker) {
        try {
            System.out.println("测试TTS功能，文本: " + text + ", 音色: " + speaker);
            
            // 生成语音
            byte[] audioData = cosyvoiceService.generateSpeech(text, speaker);

            
            // 返回结果
            Map<String, Object> result = new HashMap<>();
            result.put("success", true);
            result.put("message", "TTS生成成功");
            result.put("audio_size", audioData.length);
            
            return ResponseEntity.ok(result);
        } catch (Exception e) {
            System.err.println("TTS测试失败: " + e.getMessage());
            e.printStackTrace();
            
            Map<String, Object> error = new HashMap<>();
            error.put("success", false);
            error.put("message", "TTS测试失败: " + e.getMessage());
            
            return ResponseEntity.ok(error);
        }
    }

    @GetMapping("/status")
    public ResponseEntity<Map<String, Object>> getStatus() {
        Map<String, Object> status = new HashMap<>();
        status.put("status", "running");
        status.put("message", "语音服务正在运行");
        return ResponseEntity.ok(status);
    }
    
    @PostMapping("/force-disconnect")
    public ResponseEntity<Map<String, Object>> forceDisconnect() {
        Map<String, Object> result = new HashMap<>();
        try {
            deviceWebSocketHandler.forceDisconnectInactiveSessions();
            result.put("success", true);
            result.put("message", "已强制断开所有长时间静音的连接");
        } catch (Exception e) {
            result.put("success", false);
            result.put("message", "强制断开连接失败: " + e.getMessage());
        }
        return ResponseEntity.ok(result);
    }

    @PostMapping("/reset-audio-time")
    public ResponseEntity<Map<String, Object>> resetAudioTime() {
        Map<String, Object> result = new HashMap<>();
        try {
            vadService.forceResetAllSessionsLastAudioTime();
            result.put("success", true);
            result.put("message", "已强制重置所有会话的最后音频接收时间");
        } catch (Exception e) {
            result.put("success", false);
            result.put("message", "重置失败: " + e.getMessage());
        }
        return ResponseEntity.ok(result);
    }

    /**
     * 获取当前唤醒词配置
     */
    @GetMapping("/wake-word")
    public ResponseEntity<Map<String, Object>> getWakeWordConfig() {
        Map<String, Object> result = new HashMap<>();
        try {
            String currentWakeWord = wakeWordConfigService.getCurrentWakeWord();
            result.put("success", true);
            result.put("wake_word", currentWakeWord);
            result.put("message", "获取唤醒词配置成功");
        } catch (Exception e) {
            result.put("success", false);
            result.put("message", "获取唤醒词配置失败: " + e.getMessage());
        }
        return ResponseEntity.ok(result);
    }

    /**
     * 设置新的唤醒词
     */
    @PostMapping("/wake-word")
    public ResponseEntity<Map<String, Object>> setWakeWord(@RequestParam("wake_word") String wakeWord) {
        Map<String, Object> result = new HashMap<>();
        try {
            // 验证唤醒词格式
            if (wakeWord == null || wakeWord.trim().isEmpty()) {
                result.put("success", false);
                result.put("message", "唤醒词不能为空");
                return ResponseEntity.badRequest().body(result);
            }

            // 设置新的唤醒词
            wakeWordConfigService.setWakeWord(wakeWord.trim());

            // 通知所有连接的设备更新唤醒词配置
            int notifiedDevices = deviceWebSocketHandler.notifyWakeWordUpdate(wakeWord.trim());

            result.put("success", true);
            result.put("wake_word", wakeWord.trim());
            result.put("notified_devices", notifiedDevices);
            result.put("message", "唤醒词设置成功，已通知 " + notifiedDevices + " 个设备");
        } catch (Exception e) {
            result.put("success", false);
            result.put("message", "设置唤醒词失败: " + e.getMessage());
        }
        return ResponseEntity.ok(result);
    }

    /**
     * 获取支持的唤醒词列表
     */
    @GetMapping("/wake-word/supported")
    public ResponseEntity<Map<String, Object>> getSupportedWakeWords() {
        Map<String, Object> result = new HashMap<>();
        try {
            java.util.List<String> supportedWakeWords = wakeWordConfigService.getSupportedWakeWords();
            result.put("success", true);
            result.put("supported_wake_words", supportedWakeWords);
            result.put("message", "获取支持的唤醒词列表成功");
        } catch (Exception e) {
            result.put("success", false);
            result.put("message", "获取支持的唤醒词列表失败: " + e.getMessage());
        }
        return ResponseEntity.ok(result);
    }
}