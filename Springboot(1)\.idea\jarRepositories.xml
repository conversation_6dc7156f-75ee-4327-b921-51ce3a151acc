<?xml version="1.0" encoding="UTF-8"?>
<project version="4">
  <component name="RemoteRepositoriesConfiguration">
    <remote-repository>
      <option name="id" value="jitsi-maven-repository-snapshots" />
      <option name="name" value="Jitsi Maven Repository (Snapshots)" />
      <option name="url" value="https://github.com/jitsi/jitsi-maven-repository/raw/master/snapshots/" />
    </remote-repository>
    <remote-repository>
      <option name="id" value="jitsi-maven-repository-snapshots" />
      <option name="name" value="jitsi-maven-repository-snapshots" />
      <option name="url" value="https://github.com/jitsi/jitsi-maven-repository/raw/master/snapshots/" />
    </remote-repository>
    <remote-repository>
      <option name="id" value="central" />
      <option name="name" value="central" />
      <option name="url" value="https://repo.maven.apache.org/maven2" />
    </remote-repository>
    <remote-repository>
      <option name="id" value="central" />
      <option name="name" value="Central Repository" />
      <option name="url" value="https://repo.maven.apache.org/maven2" />
    </remote-repository>
    <remote-repository>
      <option name="id" value="jitpack.io" />
      <option name="name" value="jitpack.io" />
      <option name="url" value="https://jitpack.io" />
    </remote-repository>
    <remote-repository>
      <option name="id" value="sonatype-releases" />
      <option name="name" value="sonatype-releases" />
      <option name="url" value="https://oss.sonatype.org/content/repositories/releases/" />
    </remote-repository>
    <remote-repository>
      <option name="id" value="jitsi-maven-repository-releases" />
      <option name="name" value="jitsi-maven-repository-releases" />
      <option name="url" value="https://github.com/jitsi/jitsi-maven-repository/raw/master/releases/" />
    </remote-repository>
    <remote-repository>
      <option name="id" value="jitsi-maven-repository-releases" />
      <option name="name" value="Jitsi Maven Repository (Releases)" />
      <option name="url" value="https://github.com/jitsi/jitsi-maven-repository/raw/master/releases/" />
    </remote-repository>
    <remote-repository>
      <option name="id" value="central" />
      <option name="name" value="Maven Central repository" />
      <option name="url" value="https://repo1.maven.org/maven2" />
    </remote-repository>
    <remote-repository>
      <option name="id" value="jboss.community" />
      <option name="name" value="JBoss Community repository" />
      <option name="url" value="https://repository.jboss.org/nexus/content/repositories/public/" />
    </remote-repository>
    <remote-repository>
      <option name="id" value="sonatype-snapshots" />
      <option name="name" value="sonatype-snapshots" />
      <option name="url" value="https://oss.sonatype.org/content/repositories/snapshots/" />
    </remote-repository>
  </component>
</project>