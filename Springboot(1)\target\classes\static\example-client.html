<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>WebSocket语音助手示例</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
        }
        #status {
            color: #666;
            font-style: italic;
        }
        #result {
            margin-top: 20px;
            padding: 10px;
            border: 1px solid #ddd;
            border-radius: 4px;
            min-height: 100px;
            white-space: pre-wrap;
        }
        #recognition {
            margin-top: 10px;
            color: #0066cc;
        }
        button {
            padding: 8px 16px;
            background-color: #0066cc;
            color: white;
            border: none;
            border-radius: 4px;
            cursor: pointer;
            margin-right: 10px;
        }
        button:hover {
            background-color: #0055aa;
        }
        button:disabled {
            background-color: #cccccc;
        }
        .controls {
            margin: 20px 0;
        }
        #textInput {
            width: 80%;
            padding: 8px;
            margin-right: 10px;
        }
    </style>
</head>
<body>
    <h1>WebSocket语音助手示例</h1>
    
    <div id="status">状态: 未连接</div>
    
    <div class="controls">
        <button id="connectBtn">连接WebSocket</button>
        <button id="disconnectBtn" disabled>断开连接</button>
    </div>
    
    <div class="controls">
        <button id="startRecordBtn" disabled>开始录音</button>
        <button id="stopRecordBtn" disabled>停止录音</button>
    </div>
    
    <div class="controls">
        <input type="text" id="textInput" placeholder="输入文本..." disabled>
        <button id="sendTextBtn" disabled>发送</button>
    </div>
    
    <div id="recognition"></div>
    <div id="result"></div>
    
    <script>
        let ws = null;
        let mediaRecorder = null;
        let audioChunks = [];
        let isRecording = false;
        
        const statusElement = document.getElementById('status');
        const resultElement = document.getElementById('result');
        const recognitionElement = document.getElementById('recognition');
        const connectBtn = document.getElementById('connectBtn');
        const disconnectBtn = document.getElementById('disconnectBtn');
        const startRecordBtn = document.getElementById('startRecordBtn');
        const stopRecordBtn = document.getElementById('stopRecordBtn');
        const textInput = document.getElementById('textInput');
        const sendTextBtn = document.getElementById('sendTextBtn');
        
        // 连接WebSocket
        connectBtn.addEventListener('click', () => {
            // 创建WebSocket连接
            ws = new WebSocket(`ws://${window.location.host}/api/voice/ws`);
            
            ws.onopen = () => {
                statusElement.textContent = '状态: 已连接';
                connectBtn.disabled = true;
                disconnectBtn.disabled = false;
                startRecordBtn.disabled = false;
                textInput.disabled = false;
                sendTextBtn.disabled = false;
            };
            
            ws.onclose = () => {
                statusElement.textContent = '状态: 连接已关闭';
                resetButtons();
            };
            
            ws.onerror = (error) => {
                console.error('WebSocket错误:', error);
                statusElement.textContent = '状态: 连接错误';
                resetButtons();
            };
            
            ws.onmessage = (event) => {
                try {
                    const message = JSON.parse(event.data);
                    
                    switch(message.type) {
                        case 'status':
                            statusElement.textContent = `状态: ${message.content}`;
                            break;
                        case 'recognition':
                            recognitionElement.textContent = `识别结果: ${message.content}`;
                            break;
                        case 'message':
                            resultElement.textContent += message.content;
                            break;
                        case 'error':
                            console.error('错误:', message.content);
                            statusElement.textContent = `状态: 错误 - ${message.content}`;
                            break;
                        case 'debug':
                            console.log('调试:', message.content);
                            break;
                        default:
                            console.log('未知消息类型:', message);
                    }
                } catch (e) {
                    console.error('解析消息失败:', e, event.data);
                }
            };
        });
        
        // 断开WebSocket连接
        disconnectBtn.addEventListener('click', () => {
            if (ws) {
                ws.close();
            }
            resetButtons();
        });
        
        // 开始录音
        startRecordBtn.addEventListener('click', async () => {
            if (isRecording || !ws) return;
            
            try {
                // 清空先前的结果
                resultElement.textContent = '';
                recognitionElement.textContent = '';
                
                // 获取音频输入
                const stream = await navigator.mediaDevices.getUserMedia({ audio: true });
                
                mediaRecorder = new MediaRecorder(stream);
                audioChunks = [];
                
                mediaRecorder.ondataavailable = (event) => {
                    if (event.data.size > 0) {
                        audioChunks.push(event.data);
                    }
                };
                
                mediaRecorder.onstop = async () => {
                    // 将音频块合并为一个Blob
                    const audioBlob = new Blob(audioChunks, { type: 'audio/wav' });
                    
                    // 发送音频数据到WebSocket
                    if (ws && ws.readyState === WebSocket.OPEN) {
                        ws.send(audioBlob);
                        statusElement.textContent = '状态: 正在处理音频...';
                    }
                    
                    // 停止所有媒体轨道
                    stream.getTracks().forEach(track => track.stop());
                };
                
                // 开始录音
                mediaRecorder.start();
                isRecording = true;
                
                statusElement.textContent = '状态: 正在录音...';
                startRecordBtn.disabled = true;
                stopRecordBtn.disabled = false;
                
            } catch (err) {
                console.error('获取麦克风失败:', err);
                statusElement.textContent = `状态: 麦克风访问失败 - ${err.message}`;
            }
        });
        
        // 停止录音
        stopRecordBtn.addEventListener('click', () => {
            if (!isRecording || !mediaRecorder) return;
            
            mediaRecorder.stop();
            isRecording = false;
            
            startRecordBtn.disabled = false;
            stopRecordBtn.disabled = true;
            statusElement.textContent = '状态: 录音已停止，正在处理...';
        });
        
        // 发送文本
        sendTextBtn.addEventListener('click', () => {
            const text = textInput.value.trim();
            if (!text || !ws || ws.readyState !== WebSocket.OPEN) return;
            
            // 清空先前的结果
            resultElement.textContent = '';
            recognitionElement.textContent = '';
            
            // 创建消息对象
            const message = {
                type: 'text',
                prompt: text
            };
            
            // 发送消息
            ws.send(JSON.stringify(message));
            
            // 清空输入框
            textInput.value = '';
            
            statusElement.textContent = '状态: 正在处理文本...';
        });
        
        // 辅助函数：重置按钮状态
        function resetButtons() {
            connectBtn.disabled = false;
            disconnectBtn.disabled = true;
            startRecordBtn.disabled = true;
            stopRecordBtn.disabled = true;
            textInput.disabled = true;
            sendTextBtn.disabled = true;
            
            if (isRecording && mediaRecorder) {
                mediaRecorder.stop();
                isRecording = false;
            }
        }
    </script>
</body>
</html> 