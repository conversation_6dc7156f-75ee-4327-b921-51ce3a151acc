# 小智ESP32唤醒词动态配置功能

## 功能概述

这个功能允许你通过Spring Boot后端动态修改ESP32设备的唤醒词，无需修改ESP32前端代码。系统会通过WebSocket连接将新的唤醒词配置发送给所有连接的ESP32设备。

## 实现原理

1. **后端配置管理**: 使用`WakeWordConfigService`管理唤醒词配置
2. **WebSocket通信**: 利用现有的WebSocket连接发送配置更新消息
3. **设备状态跟踪**: 跟踪每个设备的唤醒词配置状态
4. **持久化存储**: 配置保存到本地文件，重启后自动加载

## 新增的API接口

### 1. 获取当前唤醒词配置
```
GET /api/voice/wake-word
```

**响应示例:**
```json
{
    "success": true,
    "wake_word": "小智",
    "message": "获取唤醒词配置成功"
}
```

### 2. 设置新的唤醒词
```
POST /api/voice/wake-word
Content-Type: application/x-www-form-urlencoded

wake_word=小爱
```

**响应示例:**
```json
{
    "success": true,
    "wake_word": "小爱",
    "notified_devices": 2,
    "message": "唤醒词设置成功，已通知 2 个设备"
}
```

### 3. 获取支持的唤醒词列表
```
GET /api/voice/wake-word/supported
```

**响应示例:**
```json
{
    "success": true,
    "supported_wake_words": ["小智", "小爱", "小度", "天猫精灵", "小米", "嗨小智", "你好小智", "Hi Xiaozhi", "Hello Xiaozhi"],
    "message": "获取支持的唤醒词列表成功"
}
```

## 使用方法

### 方法1: 使用Web管理界面

1. 启动Spring Boot服务
2. 在浏览器中访问: `http://localhost:8080/wake-word-config.html`
3. 在管理界面中：
   - 查看当前唤醒词配置
   - 设置新的唤醒词
   - 查看支持的唤醒词列表
   - 检查系统状态

### 方法2: 使用HTTP API

#### 使用curl命令设置唤醒词:
```bash
# 设置唤醒词为"小爱"
curl -X POST "http://localhost:8080/api/voice/wake-word" \
     -d "wake_word=小爱"

# 获取当前唤醒词
curl "http://localhost:8080/api/voice/wake-word"

# 获取支持的唤醒词列表
curl "http://localhost:8080/api/voice/wake-word/supported"
```

#### 使用Postman或其他API工具:
- URL: `http://localhost:8080/api/voice/wake-word`
- Method: `POST`
- Body: `form-data` 格式，key为`wake_word`，value为新的唤醒词

### 方法3: 使用JavaScript

```javascript
// 设置唤醒词
async function setWakeWord(newWakeWord) {
    const formData = new FormData();
    formData.append('wake_word', newWakeWord);
    
    const response = await fetch('/api/voice/wake-word', {
        method: 'POST',
        body: formData
    });
    
    const result = await response.json();
    console.log(result);
}

// 获取当前唤醒词
async function getCurrentWakeWord() {
    const response = await fetch('/api/voice/wake-word');
    const result = await response.json();
    console.log('当前唤醒词:', result.wake_word);
}

// 使用示例
setWakeWord('小爱');
```

## 配置文件

系统会在项目根目录创建`wake_word_config.properties`文件来保存配置：

```properties
#Wake Word Configuration
#Mon Jul 22 10:30:00 CST 2025
wake_word=小智
last_updated=Mon Jul 22 10:30:00 CST 2025
```

## WebSocket消息格式

当唤醒词更新时，系统会向所有连接的ESP32设备发送以下格式的消息：

```json
{
    "type": "config",
    "config_type": "wake_word",
    "wake_word": "小爱",
    "timestamp": 1642838400000
}
```

## 支持的唤醒词

系统预定义了以下支持的唤醒词：
- 小智
- 小爱
- 小度
- 天猫精灵
- 小米
- 嗨小智
- 你好小智
- Hi Xiaozhi
- Hello Xiaozhi

**注意**: 你也可以设置不在预定义列表中的唤醒词，但需要确保ESP32的ESP-SR框架支持该唤醒词。

## 工作流程

1. **设置唤醒词**: 通过API或Web界面设置新的唤醒词
2. **保存配置**: 新配置保存到本地文件
3. **通知设备**: 系统通过WebSocket向所有连接的ESP32设备发送配置更新消息
4. **设备更新**: ESP32设备接收到配置消息后更新本地唤醒词设置
5. **状态跟踪**: 系统跟踪每个设备的配置状态

## 注意事项

1. **ESP32兼容性**: 确保ESP32设备支持接收配置消息（需要在IoT消息处理中添加对应逻辑）
2. **网络连接**: 只有当前连接的设备会收到配置更新，离线设备需要重新连接后获取最新配置
3. **唤醒词格式**: 唤醒词应该是1-20个字符的中文或英文，不包含特殊字符
4. **配置持久化**: 配置会保存到本地文件，服务重启后自动加载

## 故障排除

### 问题1: 设备没有收到配置更新
- 检查设备是否正常连接到WebSocket
- 查看服务器日志确认消息是否发送成功
- 确认ESP32设备支持处理config类型的消息

### 问题2: 配置保存失败
- 检查项目目录的写入权限
- 查看服务器日志中的错误信息

### 问题3: 唤醒词不生效
- 确认ESP32设备的ESP-SR框架支持该唤醒词
- 检查唤醒词格式是否正确
- 重启ESP32设备尝试重新加载配置

## 扩展功能

你可以基于这个框架扩展更多配置功能：
- 音量设置
- 语音识别语言设置
- TTS音色设置
- 设备行为配置

只需要在`WakeWordConfigService`中添加相应的配置管理方法，并在WebSocket消息处理中添加对应的消息类型即可。
