<?xml version="1.0" encoding="UTF-8"?>
<project version="4">
  <component name="CompilerConfiguration">
    <annotationProcessing>
      <profile name="Maven default annotation processors profile" enabled="true">
        <sourceOutputDir name="target/generated-sources/annotations" />
        <sourceTestOutputDir name="target/generated-test-sources/test-annotations" />
        <outputRelativeToContentRoot value="true" />
      </profile>
      <profile name="Annotation profile for esp32-backend" enabled="true">
        <sourceOutputDir name="target/generated-sources/annotations" />
        <sourceTestOutputDir name="target/generated-test-sources/test-annotations" />
        <outputRelativeToContentRoot value="true" />
        <processorPath useClasspath="false">
          <entry name="$MAVEN_REPOSITORY$/org/projectlombok/lombok/1.18.30/lombok-1.18.30.jar" />
        </processorPath>
        <module name="esp32-backend (1)" />
      </profile>
    </annotationProcessing>
    <bytecodeTargetLevel>
      <module name="esp32-backend" target="17" />
      <module name="esp32-stt-backend" target="17" />
      <module name="xiaozhi-ai-backend" target="17" />
      <module name="xiaozhi-backend" target="17" />
    </bytecodeTargetLevel>
  </component>
  <component name="JavacSettings">
    <option name="ADDITIONAL_OPTIONS_OVERRIDE">
      <module name="esp32-backend" options="-parameters" />
      <module name="esp32-backend (1)" options="-parameters" />
      <module name="esp32-stt-backend" options="-parameters" />
      <module name="xiaozhi-ai-backend" options="-parameters" />
      <module name="xiaozhi-backend" options="-parameters" />
    </option>
  </component>
</project>