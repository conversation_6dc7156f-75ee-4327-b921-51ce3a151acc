<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>ESP32设备测试</title>
    <style>
        body {
            font-family: 'Microsoft YaHei', Arial, sans-serif;
            margin: 0;
            padding: 20px;
            background-color: #f5f5f5;
            color: #333;
        }
        .container {
            max-width: 800px;
            margin: 0 auto;
            background-color: #fff;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
        }
        h1 {
            color: #2c3e50;
            text-align: center;
            margin-bottom: 20px;
        }
        .status {
            padding: 10px;
            margin: 10px 0;
            border-radius: 4px;
            font-weight: bold;
        }
        .connected {
            background-color: #dff0d8;
            color: #3c763d;
        }
        .disconnected {
            background-color: #f2dede;
            color: #a94442;
        }
        .connecting {
            background-color: #fcf8e3;
            color: #8a6d3b;
        }
        .controls {
            margin: 20px 0;
            display: flex;
            gap: 10px;
            align-items: center;
            flex-wrap: wrap;
        }
        button {
            padding: 10px 15px;
            border: none;
            border-radius: 4px;
            cursor: pointer;
            background-color: #3498db;
            color: white;
            font-size: 16px;
            transition: background-color 0.3s;
        }
        button:hover {
            background-color: #2980b9;
        }
        button:disabled {
            background-color: #bdc3c7;
            cursor: not-allowed;
        }
        .record-btn {
            background-color: #e74c3c;
        }
        .record-btn:hover {
            background-color: #c0392b;
        }
        .record-btn.recording {
            animation: pulse 1.5s infinite;
        }
        @keyframes pulse {
            0% { background-color: #e74c3c; }
            50% { background-color: #c0392b; }
            100% { background-color: #e74c3c; }
        }
        .log-container {
            margin-top: 20px;
            border: 1px solid #ddd;
            border-radius: 4px;
            padding: 10px;
            height: 300px;
            overflow-y: auto;
            background-color: #f9f9f9;
            font-family: monospace;
            font-size: 14px;
            line-height: 1.4;
        }
        .log-entry {
            margin-bottom: 5px;
            padding-bottom: 5px;
            border-bottom: 1px solid #eee;
        }
        .log-send {
            color: #2980b9;
        }
        .log-receive {
            color: #16a085;
        }
        .log-error {
            color: #c0392b;
        }
        .audio-player {
            display: none;
            width: 100%;
            margin-top: 10px;
        }
        .form-group {
            margin-bottom: 15px;
        }
        .form-group label {
            display: block;
            margin-bottom: 5px;
            font-weight: bold;
        }
        textarea {
            width: 100%;
            height: 100px;
            padding: 8px;
            border-radius: 4px;
            border: 1px solid #ddd;
            font-family: monospace;
            resize: vertical;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>ESP32设备测试</h1>
        
        <div id="connectionStatus" class="status disconnected">
            未连接
        </div>
        
        <div class="controls">
            <button id="connectBtn">连接服务器</button>
            <button id="disconnectBtn" disabled>断开连接</button>
            <button id="helloBtn" disabled>发送Hello</button>
            <button id="listenStartBtn" disabled>开始监听</button>
            <button id="listenStopBtn" disabled>停止监听</button>
            <button id="sendAudioBtn" disabled>发送测试音频</button>
            <button id="goodbyeBtn" disabled>发送Goodbye</button>
            <button id="clearLogBtn">清空日志</button>
        </div>
        
        <div class="form-group">
            <label for="customMessage">自定义消息 (JSON):</label>
            <textarea id="customMessage" placeholder='{"type": "hello", "version": 2, "audio_params": {"format": "opus", "sample_rate": 16000, "channels": 1, "frame_duration": 20}}'></textarea>
            <button id="sendCustomBtn" disabled>发送自定义消息</button>
        </div>
        
        <h2>通信日志</h2>
        <div id="log" class="log-container"></div>
        
        <audio id="audioPlayer" class="audio-player" controls></audio>
    </div>

    <script>
        // 全局变量
        let ws = null;
        let sessionId = null;
        
        // DOM 元素
        const connectBtn = document.getElementById('connectBtn');
        const disconnectBtn = document.getElementById('disconnectBtn');
        const helloBtn = document.getElementById('helloBtn');
        const listenStartBtn = document.getElementById('listenStartBtn');
        const listenStopBtn = document.getElementById('listenStopBtn');
        const sendAudioBtn = document.getElementById('sendAudioBtn');
        const goodbyeBtn = document.getElementById('goodbyeBtn');
        const clearLogBtn = document.getElementById('clearLogBtn');
        const sendCustomBtn = document.getElementById('sendCustomBtn');
        const customMessage = document.getElementById('customMessage');
        const log = document.getElementById('log');
        const connectionStatus = document.getElementById('connectionStatus');
        const audioPlayer = document.getElementById('audioPlayer');
        
        // 测试音频数据（可以替换为实际的OPUS编码音频）
        const testAudioData = new Uint8Array(100);
        for (let i = 0; i < testAudioData.length; i++) {
            testAudioData[i] = Math.floor(Math.random() * 256);
        }
        
        // 设置测试hello消息
        customMessage.value = JSON.stringify({
            "type": "hello",
            "version": 2,
            "audio_params": {
                "format": "opus",
                "sample_rate": 16000,
                "channels": 1,
                "frame_duration": 20
            },
            "features": {
                "aec": true,
                "mcp": false
            }
        }, null, 2);
        
        // 连接WebSocket
        connectBtn.addEventListener('click', () => {
            connectWebSocket();
        });
        
        // 断开WebSocket连接
        disconnectBtn.addEventListener('click', () => {
            if (ws) {
                ws.close();
            }
        });
        
        // 发送Hello消息
        helloBtn.addEventListener('click', () => {
            if (ws) {
                const helloMessage = {
                    type: "hello",
                    version: 2,
                    audio_params: {
                        format: "opus",
                        sample_rate: 16000,
                        channels: 1,
                        frame_duration: 20
                    },
                    features: {
                        aec: true,
                        mcp: false
                    }
                };
                sendJsonMessage(helloMessage);
            }
        });
        
        // 开始监听
        listenStartBtn.addEventListener('click', () => {
            if (ws) {
                const listenMessage = {
                    type: "listen",
                    state: "start",
                    mode: "auto"
                };
                sendJsonMessage(listenMessage);
            }
        });
        
        // 停止监听
        listenStopBtn.addEventListener('click', () => {
            if (ws) {
                const listenMessage = {
                    type: "listen",
                    state: "stop"
                };
                sendJsonMessage(listenMessage);
            }
        });
        
        // 发送测试音频
        sendAudioBtn.addEventListener('click', () => {
            if (ws) {
                // 创建带头部的OPUS音频包
                const headerSize = 16;
                const packetData = new Uint8Array(headerSize + testAudioData.length);
                
                // 类型 (1 = OPUS)
                packetData[0] = 0x01;
                // 标志
                packetData[1] = 0x00;
                // 数据大小 (2字节)
                packetData[2] = testAudioData.length & 0xFF;
                packetData[3] = (testAudioData.length >> 8) & 0xFF;
                // SSRC (4字节)
                packetData[4] = 0;
                packetData[5] = 0;
                packetData[6] = 0;
                packetData[7] = 0;
                // 时间戳 (4字节)
                const timestamp = Math.floor(Date.now() % 0xFFFFFFFF);
                packetData[8] = timestamp & 0xFF;
                packetData[9] = (timestamp >> 8) & 0xFF;
                packetData[10] = (timestamp >> 16) & 0xFF;
                packetData[11] = (timestamp >> 24) & 0xFF;
                // 序列号 (4字节)
                const sequence = 1;
                packetData[12] = sequence & 0xFF;
                packetData[13] = (sequence >> 8) & 0xFF;
                packetData[14] = (sequence >> 16) & 0xFF;
                packetData[15] = (sequence >> 24) & 0xFF;
                
                // 复制测试音频数据
                packetData.set(testAudioData, headerSize);
                
                // 发送二进制数据
                ws.send(packetData.buffer);
                
                // 记录日志
                logMessage("发送", "二进制音频数据: " + packetData.length + " 字节");
            }
        });
        
        // 发送Goodbye消息
        goodbyeBtn.addEventListener('click', () => {
            if (ws && sessionId) {
                const goodbyeMessage = {
                    type: "goodbye",
                    session_id: sessionId
                };
                sendJsonMessage(goodbyeMessage);
            }
        });
        
        // 清空日志
        clearLogBtn.addEventListener('click', () => {
            log.innerHTML = '';
        });
        
        // 发送自定义消息
        sendCustomBtn.addEventListener('click', () => {
            if (ws) {
                try {
                    const message = JSON.parse(customMessage.value);
                    sendJsonMessage(message);
                } catch (error) {
                    logMessage("错误", "无效的JSON格式: " + error.message, true);
                }
            }
        });
        
        // 连接WebSocket服务器
        function connectWebSocket() {
            if (ws) {
                ws.close();
            }
            
            // 更新状态
            connectionStatus.className = 'status connecting';
            connectionStatus.textContent = '正在连接...';
            
            // 创建WebSocket连接
            const protocol = window.location.protocol === 'https:' ? 'wss:' : 'ws:';
            const wsUrl = `${protocol}//${window.location.host}/api/device/ws`;
            
            ws = new WebSocket(wsUrl);
            
            // 连接建立时
            ws.onopen = () => {
                connectionStatus.className = 'status connected';
                connectionStatus.textContent = '已连接';
                
                updateButtonStates(true);
                
                logMessage("系统", "WebSocket连接已建立");
            };
            
            // 收到消息时
            ws.onmessage = (event) => {
                if (event.data instanceof Blob) {
                    // 处理二进制数据
                    handleBinaryMessage(event.data);
                } else {
                    // 处理文本数据
                    const message = JSON.parse(event.data);
                    logMessage("接收", JSON.stringify(message, null, 2));
                    
                    // 处理会话ID
                    if (message.type === "hello" && message.session_id) {
                        sessionId = message.session_id;
                        logMessage("系统", "会话ID已设置: " + sessionId);
                    }
                }
            };
            
            // 连接关闭时
            ws.onclose = () => {
                connectionStatus.className = 'status disconnected';
                connectionStatus.textContent = '已断开连接';
                
                updateButtonStates(false);
                
                sessionId = null;
                ws = null;
                logMessage("系统", "WebSocket连接已关闭");
            };
            
            // 连接错误时
            ws.onerror = (error) => {
                logMessage("错误", "WebSocket错误", true);
                connectionStatus.className = 'status disconnected';
                connectionStatus.textContent = '连接错误';
            };
        }
        
        // 处理二进制消息
        function handleBinaryMessage(blob) {
            logMessage("接收", "二进制数据: " + blob.size + " 字节");
            
            // 读取二进制数据
            const reader = new FileReader();
            reader.onload = () => {
                const arrayBuffer = reader.result;
                const dataView = new DataView(arrayBuffer);
                
                // 解析头部
                try {
                    // 检查是否至少有头部
                    if (arrayBuffer.byteLength < 16) {
                        logMessage("错误", "收到的二进制数据太短，无法解析头部", true);
                        return;
                    }
                    
                    // 解析头部
                    const type = dataView.getUint8(0);
                    const flags = dataView.getUint8(1);
                    const dataSize = dataView.getUint16(2, true);
                    const ssrc = dataView.getUint32(4, true);
                    const timestamp = dataView.getUint32(8, true);
                    const sequence = dataView.getUint32(12, true);
                    
                    // 记录头部信息
                    logMessage("系统", `音频包头部: 类型=${type}, 标志=${flags}, 大小=${dataSize}, SSRC=${ssrc}, 时间戳=${timestamp}, 序列号=${sequence}`);
                    
                    // 检查数据大小是否匹配
                    if (arrayBuffer.byteLength < 16 + dataSize) {
                        logMessage("错误", "音频数据长度不匹配", true);
                        return;
                    }
                    
                    // 如果是OPUS音频，可以尝试播放（需要转换为WAV或其他浏览器支持的格式）
                    if (type === 1) {
                        // 注意：真实场景中，这里需要将OPUS转换为浏览器可播放的格式
                        // 这里仅作为示例，不进行实际转换和播放
                        logMessage("系统", "收到OPUS音频数据，长度: " + dataSize + " 字节");
                    }
                } catch (error) {
                    logMessage("错误", "解析二进制数据失败: " + error.message, true);
                }
            };
            reader.readAsArrayBuffer(blob);
        }
        
        // 发送JSON消息
        function sendJsonMessage(message) {
            if (ws && ws.readyState === WebSocket.OPEN) {
                const jsonStr = JSON.stringify(message);
                ws.send(jsonStr);
                logMessage("发送", jsonStr);
            }
        }
        
        // 记录消息到日志
        function logMessage(direction, message, isError = false) {
            const entry = document.createElement('div');
            entry.className = `log-entry log-${isError ? 'error' : (direction.toLowerCase() === '接收' ? 'receive' : 'send')}`;
            
            const timestamp = new Date().toLocaleTimeString();
            entry.textContent = `[${timestamp}] [${direction}] ${message}`;
            
            log.appendChild(entry);
            log.scrollTop = log.scrollHeight;
        }
        
        // 更新按钮状态
        function updateButtonStates(connected) {
            connectBtn.disabled = connected;
            disconnectBtn.disabled = !connected;
            helloBtn.disabled = !connected;
            listenStartBtn.disabled = !connected;
            listenStopBtn.disabled = !connected;
            sendAudioBtn.disabled = !connected;
            goodbyeBtn.disabled = !connected || !sessionId;
            sendCustomBtn.disabled = !connected;
        }
    </script>
</body>
</html> 