package com.example.service;

import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.databind.node.ArrayNode;
import com.fasterxml.jackson.databind.node.ObjectNode;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.http.HttpEntity;
import org.springframework.http.HttpHeaders;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Service;
import org.springframework.web.client.RestTemplate;
import com.example.service.VoiceWebSocketHandler.WebSocketMessageSender;

import jakarta.annotation.PostConstruct;
import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.HashMap;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.atomic.AtomicBoolean;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;
import java.net.URI;
import java.io.IOException;

/**
 * VLLM服务 - 处理与大语言模型的交互
 */
@Service
public class VllmService {

    @Value("${vllm.api.url}")
    private String apiUrl;

    @Value("${vllm.model.name:default}")
    private String modelName;

    @Value("${vllm.api.key:}")
    private String apiKey;

    @Value("${vllm.api.timeout:60000}")
    private int timeout;
    
    @Value("${vllm.enable.thinking:false}")
    private boolean enableThinking;
    
    @Value("${vllm.max.retries:3}")
    private int maxRetries;
    
    @Value("${vllm.retry.delay:1000}")
    private int retryDelayMs;
    
    @Value("${vllm.system-prompt:你是一个智能语音助手，请简洁明了地回答问题。}")
    private String systemPrompt;
    
    @Value("${vllm.api.url}")
    private String vllmApiUrl;

    private final RestTemplate restTemplate;
    private final ObjectMapper objectMapper;
    private final ExecutorService executorService = Executors.newCachedThreadPool();

    // 保存对话历史
    private final List<Message> conversationHistory = Collections.synchronizedList(new ArrayList<>());
    private static final int MAX_HISTORY_LENGTH = 10; // 最大历史消息数量

    public VllmService() {
        this.restTemplate = new RestTemplate();
        this.objectMapper = new ObjectMapper();
    }

    @PostConstruct
    public void init() {
        System.out.println("VLLM服务初始化...");
        System.out.println("API URL: " + apiUrl);
        
        // 确保系统提示词使用UTF-8编码
        try {
            // 使用UTF-8解码系统提示词，避免乱码
            byte[] bytes = systemPrompt.getBytes("ISO-8859-1");
            systemPrompt = new String(bytes, "UTF-8");
            System.out.println("系统提示词: " + systemPrompt);
            
            // 添加系统提示词作为第一条消息
            conversationHistory.add(new Message("system", systemPrompt));
        } catch (Exception e) {
            System.err.println("系统提示词编码转换失败: " + e.getMessage());
            e.printStackTrace();
            // 使用默认提示词
            systemPrompt = "你是一个智能语音助手，请简洁明了地回答问题。";
            conversationHistory.add(new Message("system", systemPrompt));
        }
    }

    /**
     * 处理用户输入文本，获取AI响应
     *
     * @param text 用户输入文本
     * @return AI响应文本
     */
    public String process(String text) {
        if (text == null || text.trim().isEmpty()) {
            System.err.println("输入文本为空");
            return "抱歉，我没有听清楚你说什么";
        }
        
        // 检查是否是特殊的退出命令标记
        if ("$EXIT_COMMAND$".equals(text)) {
            System.out.println("检测到退出命令特殊标记，不调用VLLM");
            return "好的，我们的对话已经结束，下次再见！";
        }
        
        // 检查是否是长时间无语音的消息
        if (text != null && (text.contains("很久没说话") || text.contains("需要继续对话") || 
                            text.contains("您似乎已经很久没说话") || text.contains("超时") || 
                            text.contains("timeout"))) {
            System.out.println("检测到超时消息，直接返回不调用VLLM: " + text);
            return text;
        }

        try {
            long startTime = System.currentTimeMillis();

            // 将用户消息添加到历史记录
            conversationHistory.add(new Message("user", text));

            // 构建请求
            HttpHeaders headers = new HttpHeaders();
            headers.setContentType(MediaType.APPLICATION_JSON);
            if (apiKey != null && !apiKey.isEmpty()) {
                headers.set("Authorization", "Bearer " + apiKey);
            }

            // 创建请求体
            ObjectNode requestBody = objectMapper.createObjectNode();
            requestBody.put("model", modelName);
            requestBody.put("temperature", 0.7);
            requestBody.put("top_p", 0.95);
            requestBody.put("max_tokens", retryDelayMs);
            
            // 添加chat_template_kwargs参数以禁用思考模式
            ObjectNode templateKwargs = objectMapper.createObjectNode();
            templateKwargs.put("enable_thinking", false);
            requestBody.set("chat_template_kwargs", templateKwargs);
                
            // 添加消息历史
            ArrayNode messages = requestBody.putArray("messages");
            for (Message msg : conversationHistory) {
                ObjectNode messageObj = messages.addObject();
                messageObj.put("role", msg.getRole());
                messageObj.put("content", msg.getContent());
                }

            // 发送请求
            HttpEntity<String> entity = new HttpEntity<>(requestBody.toString(), headers);
            System.out.println("发送请求到VLLM: " + requestBody.toString());
                
            ResponseEntity<String> response = restTemplate.postForEntity(apiUrl, entity, String.class);
            JsonNode responseJson = objectMapper.readTree(response.getBody());

            long endTime = System.currentTimeMillis();
            System.out.println("VLLM响应时间: " + (endTime - startTime) + "ms");

            // 解析响应
            String aiResponse = parseResponse(responseJson);
            if (aiResponse != null && !aiResponse.isEmpty()) {
                // 将AI回复添加到历史记录
                conversationHistory.add(new Message("assistant", aiResponse));
                
                // 如果历史记录过长，移除旧的消息
                while (conversationHistory.size() > MAX_HISTORY_LENGTH + 1) { // +1是系统提示词
                    if (conversationHistory.get(0).getRole().equals("system")) {
                        // 如果是系统提示词，从第二条开始移除
                        conversationHistory.remove(1);
                    } else {
                        conversationHistory.remove(0);
                    }
                }
            }

            return aiResponse;
        } catch (Exception e) {
            System.err.println("调用VLLM API失败: " + e.getMessage());
            e.printStackTrace();
            return "抱歉，我现在无法回答你的问题，请稍后再试";
        }
    }

    /**
     * 清除对话历史
     */
    public void clearHistory() {
        synchronized (conversationHistory) {
            conversationHistory.clear();
            // 重新添加系统提示词
            conversationHistory.add(new Message("system", systemPrompt));
        }
        System.out.println("对话历史已清除");
    }

    /**
     * 解析VLLM API响应
     */
    private String parseResponse(JsonNode responseJson) {
        try {
            JsonNode choicesNode = responseJson.get("choices");
            if (choicesNode != null && choicesNode.isArray() && choicesNode.size() > 0) {
                JsonNode firstChoice = choicesNode.get(0);
                if (firstChoice != null) {
                    JsonNode messageNode = firstChoice.get("message");
                    if (messageNode != null) {
                        JsonNode contentNode = messageNode.get("content");
                        if (contentNode != null && contentNode.isTextual()) {
                            return contentNode.asText();
                        }
                    }
                }
            }
            System.err.println("无法解析VLLM响应: " + responseJson);
            return "抱歉，我无法理解这个问题";
        } catch (Exception e) {
            System.err.println("解析VLLM响应失败: " + e.getMessage());
            e.printStackTrace();
            return "抱歉，处理你的请求时出现了问题";
        }
    }
    
    /**
     * 获取当前对话消息历史
     * @return 消息历史列表的副本
     */
    public List<Map<String, String>> getMessages() {
        List<Map<String, String>> result = new ArrayList<>();
        synchronized (conversationHistory) {
            for (Message message : conversationHistory) {
                Map<String, String> messageMap = new HashMap<>();
                messageMap.put("role", message.getRole());
                messageMap.put("content", message.getContent());
                result.add(messageMap);
            }
        }
        return result;
    }

    /**
     * 消息类 - 用于存储对话历史
     */
    private static class Message {
        private final String role;
        private final String content;

        public Message(String role, String content) {
            this.role = role;
            this.content = content;
        }

        public String getRole() {
            return role;
        }

        public String getContent() {
            return content;
        }
    }
    
    /**
     * 使用WebSocket处理文本并流式返回
     *
     * @param prompt 输入文本
     * @param messageSender WebSocket消息发送器
     */
    public void processTextWithWebSocket(String prompt, WebSocketMessageSender messageSender) {
        executorService.submit(() -> {
            // 使用AtomicBoolean替代普通boolean，以便在lambda中使用
            final AtomicBoolean success = new AtomicBoolean(false);
            Exception lastException = null;
            
            // 发送初始消息
            Map<String, Object> statusData = new HashMap<>();
            statusData.put("message", "正在连接vLLM模型...");
            try {
                messageSender.sendWebSocketMessage("status", statusData);
            } catch (IOException e) {
                System.err.println("发送初始状态消息失败: " + e.getMessage());
            }
            
            // 发送处理的提示文本
            System.out.println("发送处理的提示文本: " + prompt);
            Map<String, Object> debugData = new HashMap<>();
            debugData.put("message", "准备处理文本: " + prompt);
            try {
                messageSender.sendWebSocketMessage("debug", debugData);
            } catch (IOException e) {
                System.err.println("发送调试消息失败: " + e.getMessage());
            }
            
            for (int attempt = 0; attempt < maxRetries; attempt++) {
                try {
                    if (attempt > 0) {
                        System.out.println("vLLM流式处理重试第 " + (attempt + 1) + " 次");
                        try {
                            Thread.sleep(retryDelayMs * attempt);
                        } catch (InterruptedException ie) {
                            Thread.currentThread().interrupt();
                        }
                    }
                    
                    // 准备请求头
                    HttpHeaders headers = new HttpHeaders();
                    headers.setContentType(MediaType.APPLICATION_JSON);
                    if (apiKey != null && !apiKey.isEmpty()) {
                        headers.set("Authorization", "Bearer " + apiKey);
                    }
    
                    // 准备请求体 - vLLM使用OpenAI兼容格式
                    Map<String, Object> requestBody = new HashMap<>();
                    requestBody.put("model", modelName);
                    
                    // 构建messages数组
                    List<Map<String, Object>> messages = new ArrayList<>();
                    Map<String, Object> userMessage = new HashMap<>();
                    userMessage.put("role", "user");
                    userMessage.put("content", prompt);
                    messages.add(userMessage);
                    
                    requestBody.put("messages", messages);
                    requestBody.put("stream", true);  // 启用流式输出
                    
                    // 添加chat_template_kwargs参数以控制thinking模式
                    if (!enableThinking) {
                        Map<String, Object> kwargs = new HashMap<>();
                        kwargs.put("enable_thinking", false);
                        requestBody.put("chat_template_kwargs", kwargs);
                    }
    
                    // 创建请求实体
                    HttpEntity<Map<String, Object>> requestEntity = new HttpEntity<>(requestBody, headers);
    
                    // 打印调试信息
                    System.out.println("发送vLLM流式请求到: " + vllmApiUrl);
                    System.out.println("使用模型: " + modelName + ", 启用思考模式: " + enableThinking);
                    
                    // 使用特殊的流式处理方法
                    StreamingRestTemplate streamingRestTemplate = new StreamingRestTemplate();
                    
                    // 发送处理状态
                    Map<String, Object> processingData = new HashMap<>();
                    processingData.put("message", "AI正在思考...");
                    messageSender.sendWebSocketMessage("status", processingData);
                    
                    // 发送请求并处理SSE流
                    StringBuilder fullResponse = new StringBuilder();
                    streamingRestTemplate.postForStream(vllmApiUrl, requestEntity, line -> {
                            try {
                            // 解析SSE数据行
                            if (line.startsWith("data: ")) {
                                String data = line.substring(6);
                                if (data.equals("[DONE]")) {
                                    // 流结束
                                    return;
                                }
                                
                                // 解析JSON
                                JsonNode root = objectMapper.readTree(data);
                                JsonNode choices = root.get("choices");
                                if (choices != null && choices.isArray() && choices.size() > 0) {
                                    JsonNode choice = choices.get(0);
                                    JsonNode delta = choice.get("delta");
                                    if (delta != null) {
                                        JsonNode content = delta.get("content");
                                        if (content != null && content.isTextual()) {
                                            String text = content.asText();
                                            if (!text.isEmpty()) {
                                                // 添加到完整响应
                                                fullResponse.append(text);
                                                
                                                // 发送增量更新
                                                Map<String, Object> messageData = new HashMap<>();
                                                messageData.put("text", text);
                                                messageData.put("done", false);
                                                messageSender.sendWebSocketMessage("message", messageData);
                                            }
                                        }
                                    }
                                }
                            }
                            } catch (Exception e) {
                            System.err.println("处理流式响应行时出错: " + e.getMessage());
                            e.printStackTrace();
                        }
                    });
                    
                    // 发送完成消息
                    Map<String, Object> completeData = new HashMap<>();
                    completeData.put("text", fullResponse.toString());
                    completeData.put("done", true);
                    messageSender.sendWebSocketMessage("message", completeData);
                    
                    // 发送完成状态
                    messageSender.complete();
                    
                    success.set(true);
                    break;
                } catch (Exception e) {
                    System.err.println("vLLM流式处理失败: " + e.getMessage());
                    e.printStackTrace();
                    lastException = e;
                }
            }
            
            // 如果所有尝试都失败
            if (!success.get() && lastException != null) {
                System.err.println("所有vLLM流式处理尝试都失败，尝试备用方法: " + lastException.getMessage());
                
                try {
                    // 尝试使用非流式API
                    String response = process(prompt);
                    Map<String, Object> messageData = new HashMap<>();
                    messageData.put("text", response);
                    messageData.put("done", true);
                    messageSender.sendWebSocketMessage("message", messageData);
                    messageSender.complete();
                } catch (Exception e) {
                    System.err.println("所有处理方法都失败: " + e.getMessage());
                    Map<String, Object> errorData = new HashMap<>();
                    errorData.put("message", "AI处理失败: " + e.getMessage());
                    try {
                        messageSender.sendWebSocketMessage("error", errorData);
                    messageSender.complete();
                    } catch (IOException ex) {
                        System.err.println("发送错误消息失败: " + ex.getMessage());
                    }
                }
            }
        });
    }
    
    /**
     * 在服务关闭时释放资源
     */
    public void shutdown() {
        executorService.shutdown();
    }
    
    /**
     * 内部类：支持流式响应的RestTemplate
     */
    private static class StreamingRestTemplate {
        /**
         * 发送HTTP请求并以流式方式处理SSE响应
         */
        public void postForStream(String url, HttpEntity<?> requestEntity, java.util.function.Consumer<String> lineConsumer) {
            try {
                // 使用HttpURLConnection发送请求
                URI uri = new URI(url);
                java.net.HttpURLConnection connection = (java.net.HttpURLConnection) uri.toURL().openConnection();
                connection.setRequestMethod("POST");
                
                // 设置请求头
                requestEntity.getHeaders().forEach((name, values) -> {
                    for (String value : values) {
                        connection.setRequestProperty(name, value);
                    }
                });
                
                // 设置连接属性
                connection.setDoOutput(true);
                connection.setConnectTimeout(30000);
                connection.setReadTimeout(60000);
                
                // 写入请求体
                if (requestEntity.getBody() != null) {
                    String requestBody;
                    if (requestEntity.getBody() instanceof String) {
                        requestBody = (String) requestEntity.getBody();
                    } else {
                        requestBody = new ObjectMapper().writeValueAsString(requestEntity.getBody());
                    }
                
                try (java.io.OutputStream os = connection.getOutputStream()) {
                        os.write(requestBody.getBytes(java.nio.charset.StandardCharsets.UTF_8));
                    }
                }
                
                // 获取响应
                int responseCode = connection.getResponseCode();
                if (responseCode >= 200 && responseCode < 300) {
                    // 读取响应体
                    try (java.io.BufferedReader reader = new java.io.BufferedReader(
                            new java.io.InputStreamReader(connection.getInputStream(), java.nio.charset.StandardCharsets.UTF_8))) {
                        String line;
                        while ((line = reader.readLine()) != null) {
                            if (!line.trim().isEmpty()) {
                                lineConsumer.accept(line);
                            }
                        }
                    }
                } else {
                    // 处理错误响应
                    StringBuilder errorMessage = new StringBuilder("HTTP错误 ");
                    errorMessage.append(responseCode).append(": ");
                    
                    try (java.io.BufferedReader reader = new java.io.BufferedReader(
                            new java.io.InputStreamReader(connection.getErrorStream(), java.nio.charset.StandardCharsets.UTF_8))) {
                    String line;
                        while ((line = reader.readLine()) != null) {
                            errorMessage.append(line);
                        }
                    }
                    
                    throw new RuntimeException(errorMessage.toString());
                }
            } catch (Exception e) {
                throw new RuntimeException("流式请求失败: " + e.getMessage(), e);
            }
        }
    }
} 