package com.example.service;

import com.fasterxml.jackson.databind.ObjectMapper;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.http.HttpEntity;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpMethod;
import org.springframework.http.HttpStatus;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Service;
import org.springframework.web.client.RestClientException;
import org.springframework.web.client.RestTemplate;
import org.springframework.web.socket.TextMessage;
import org.springframework.web.socket.WebSocketSession;

import jakarta.annotation.PreDestroy;
import java.io.IOException;
import java.nio.charset.StandardCharsets;
import java.util.HashMap;
import java.util.Map;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;
import java.util.function.Consumer;
import java.util.regex.Pattern;
import java.util.UUID;
import java.util.Collections;

/**
 * CosyVoice语音合成服务
 */
@Service
public class CosyvoiceService {

    private final RestTemplate restTemplate;
    private final ObjectMapper objectMapper;
    private final ExecutorService executorService = Executors.newCachedThreadPool();

    @Value("${cosyvoice.api.url:http://localhost:8000}")
    private String cosyvoiceApiUrl;

    @Value("${cosyvoice.default-speaker:中文女}")
    private String defaultSpeaker;

    @Value("${cosyvoice.complexity:8}")
    private int complexity;

    public CosyvoiceService() {
        // 创建RestTemplate实例，使用默认配置
        this.restTemplate = new RestTemplate();
        this.objectMapper = new ObjectMapper();
    }

    // 获取默认音色
    public String getDefaultSpeaker() {
        return defaultSpeaker;
    }

    // 获取音频质量参数
    public int getComplexity() {
        return complexity;
    }

    /**
     * 获取可用的语音合成音色列表
     *
     * @return 音色列表数组
     */
    public String[] getSpeakers() {
        try {
            // 调用CosyVoice API获取音色列表
            String speakersUrl = cosyvoiceApiUrl + "/speakers";
            System.out.println("获取音色列表: " + speakersUrl);

            ResponseEntity<Map> response = restTemplate.getForEntity(speakersUrl, Map.class);

            if (response.getStatusCode() == HttpStatus.OK && response.getBody() != null) {
                System.out.println("获取音色列表成功: " + response.getBody());

                if (response.getBody().containsKey("speakers")) {
                    java.util.List<String> speakers = (java.util.List<String>) response.getBody().get("speakers");
                    System.out.println("找到的音色: " + speakers);
                    return speakers.toArray(new String[0]);
                } else {
                    System.err.println("响应中没有找到'speakers'字段: " + response.getBody());
                }
            } else {
                System.err.println("获取音色列表失败，状态码: " + response.getStatusCode());
            }

            // 如果无法获取音色列表，返回默认音色
            System.out.println("使用默认音色列表");
            return new String[]{"中文女", "中文男", "英文女", "英文男"};
        } catch (Exception e) {
            System.err.println("获取音色列表异常: " + e.getMessage());
            e.printStackTrace();
            // 返回默认音色
            return new String[]{"中文女", "中文男", "英文女", "英文男"};
        }
    }

    /**
     * 处理AI回复文本，生成语音，并通过WebSocket发送给前端
     *
     * @param session WebSocket会话
     * @param text 要转换成语音的文本
     * @param speaker 选择的音色
     */
    public void processTextToSpeech(WebSocketSession session, String text, String speaker) {
        processTextToSpeech(session, text, speaker, null);
    }

    /**
     * 处理AI回复文本，生成语音，并通过WebSocket发送给前端
     * 带有完成回调功能的重载方法
     *
     * @param session WebSocket会话
     * @param text 要转换成语音的文本
     * @param speaker 选择的音色
     * @param completionCallback 完成后的回调函数
     */
    public void processTextToSpeech(WebSocketSession session, String text, String speaker, Runnable completionCallback) {
        // 如果文本为空，直接返回
        if (text == null || text.trim().isEmpty()) {
            try {
                sendMessage(session, "error", "文本内容为空，无法生成语音");
                if (completionCallback != null) {
                    completionCallback.run();
                }
            } catch (IOException e) {
                System.err.println("发送错误消息失败: " + e.getMessage());
                if (completionCallback != null) {
                    completionCallback.run();
                }
            }
            return;
        }

        // 使用线程池异步处理
        executorService.submit(() -> {
            try {
                // 如果音色为空，使用默认音色
                String actualSpeaker = (speaker == null || speaker.trim().isEmpty()) ? "中文女" : speaker;

                // 发送处理状态
                sendMessage(session, "status", "正在生成语音...");

                // 过滤表情符号
                String filteredText = filterEmoji(text);

                // 分段处理长文本
                String[] segments = segmentText(filteredText);

                if (segments.length == 0) {
                    // 没有可处理的段落
                    sendMessage(session, "status", "没有可处理的文本");
                    if (completionCallback != null) {
                        completionCallback.run();
                    }
                    return;
                }

                // 创建一个队列来存储预处理好的段落
                final java.util.concurrent.ConcurrentLinkedQueue<ProcessedSegment> processedQueue = new java.util.concurrent.ConcurrentLinkedQueue<>();

                // 创建计数器来追踪完成的段落数量和发送的段落数量
                final java.util.concurrent.atomic.AtomicInteger completedSegments = new java.util.concurrent.atomic.AtomicInteger(0);
                final java.util.concurrent.atomic.AtomicInteger sentSegments = new java.util.concurrent.atomic.AtomicInteger(0);

                // 创建互斥锁，用于发送的同步
                final Object sendLock = new Object();

                // 预处理的段落数量上限（控制并发度）
                final int MAX_PREPROCESS_COUNT = Math.min(segments.length, 3); // 最多预处理3个段落
                System.out.println("设置最大预处理段落数: " + MAX_PREPROCESS_COUNT);

                // 段落处理完成的回调
                Consumer<ProcessedSegment> segmentProcessedCallback = new Consumer<ProcessedSegment>() {
                    @Override
                    public void accept(ProcessedSegment processedSegment) {
                        // 将处理好的段落添加到队列
                        processedQueue.add(processedSegment);
                        System.out.println("段落 " + (processedSegment.index + 1) + "/" + segments.length + " 处理完成，已添加到队列");

                        synchronized (sendLock) {
                            // 通知发送线程有新段落可用
                            sendLock.notify();
                        }

                        // 增加完成计数
                        int completed = completedSegments.incrementAndGet();

                        // 如果还有更多段落需要处理，且未达到最大预处理数，则继续处理下一个段落
                        int nextToProcess = completed + MAX_PREPROCESS_COUNT - 1;
                        if (nextToProcess < segments.length) {
                            processSegmentAsync(segments[nextToProcess], actualSpeaker, this);
                            System.out.println("开始预处理下一段: " + (nextToProcess + 1) + "/" + segments.length);
                        }
                    }
                };

                // 启动初始的段落处理（根据MAX_PREPROCESS_COUNT决定初始并发数）
                for (int i = 0; i < MAX_PREPROCESS_COUNT; i++) {
                    if (i < segments.length) {
                        System.out.println("初始化处理段落 " + (i + 1) + "/" + segments.length);
                        processSegmentAsync(segments[i], actualSpeaker, segmentProcessedCallback);
                    }
                }

                // 创建发送线程，负责按顺序发送处理好的段落
                Thread sendThread = new Thread(() -> {
                    try {
                        long audioStartTime = System.currentTimeMillis();
                        int totalBytesSent = 0;
                        int totalFramesSent = 0;

                        while (sentSegments.get() < segments.length) {
                            ProcessedSegment segment = null;

                            // 尝试获取下一个要发送的段落
                            synchronized (sendLock) {
                                while (processedQueue.isEmpty() && sentSegments.get() < segments.length) {
                                    // 等待新段落可用
                                    try {
                                        System.out.println("等待段落 " + (sentSegments.get() + 1) + "/" + segments.length + " 处理完成...");
                                        sendLock.wait(500); // 最多等待500毫秒
                                    } catch (InterruptedException e) {
                                        Thread.currentThread().interrupt();
                                        System.err.println("发送线程等待被中断: " + e.getMessage());
                                        break;
                                    }
                                }

                                // 检查队列中是否有可发送的段落
                                if (!processedQueue.isEmpty()) {
                                    // 查找下一个要发送的段落
                                    int nextToSend = sentSegments.get();
                                    for (ProcessedSegment ps : processedQueue) {
                                        if (ps.index == nextToSend) {
                                            segment = ps;
                                            processedQueue.remove(ps);
                                            break;
                                        }
                                    }
                                }
                            }

                            // 如果找到了下一个要发送的段落，则发送
                            if (segment != null) {
                                // 更新状态
                                sendMessage(session, "status", "正在播放第 " + (segment.index + 1) + "/" + segments.length + " 段...");

                                // 构造响应数据
                                Map<String, Object> audioMessage = new HashMap<>();
                                audioMessage.put("type", "audio");
                                audioMessage.put("text", segment.text);
                                audioMessage.put("isLast", (segment.index == segments.length - 1));
                                audioMessage.put("index", segment.index);
                                audioMessage.put("total", segments.length);
                                audioMessage.put("segment_id", UUID.randomUUID().toString());

                                // 如果有音频数据，则发送二进制音频数据
                                if (segment.audioData != null && segment.audioData.length > 0) {
                                    sendBinaryMessage(session, audioMessage, segment.audioData);

                                    // 统计总发送量
                                    totalBytesSent += segment.audioData.length;
                                    totalFramesSent++;

                                    System.out.println("已发送段落 " + (segment.index + 1) + "/" + segments.length +
                                                      ", 字节数: " + segment.audioData.length);
                                } else {
                                    // 如果没有音频数据，仅发送文本标记
                                    try {
                                        String json = objectMapper.writeValueAsString(audioMessage);
                                        synchronized(session) {
                                            session.sendMessage(new TextMessage(json));
                                        }
                                        System.out.println("段落 " + (segment.index + 1) + " 无音频数据，仅发送文本标记");
                                    } catch (IOException e) {
                                        System.err.println("发送文本标记失败: " + e.getMessage());
                                    }
                                }

                                // 增加发送计数
                                sentSegments.incrementAndGet();

                                // 添加短暂延迟，避免段落之间发送过快
                                if (segment.index < segments.length - 1) {
                                    try {
                                        // 根据段落大小动态调整延迟，越大的段落延迟越短
                                        long delayMs = Math.max(50, 200 - (segment.audioData != null ? segment.audioData.length / 1000 : 0));
                                        Thread.sleep(delayMs);
                                    } catch (InterruptedException e) {
                                        Thread.currentThread().interrupt();
                                    }
                                }
                            }
                        }

                        // 计算总体语音合成时间和速度
                        long totalTime = System.currentTimeMillis() - audioStartTime;
                        double bytesPerSecond = totalBytesSent / (totalTime / 1000.0);

                        System.out.println("语音合成完成: " + segments.length + " 段, " +
                                          totalBytesSent + " 字节, " +
                                          totalFramesSent + " 帧, " +
                                          totalTime + "ms, " +
                                          String.format("%.2f", bytesPerSecond) + " 字节/秒");

                        // 发送处理完成状态
                        sendMessage(session, "status", "语音生成完成");

                        // 执行完成回调
                        if (completionCallback != null) {
                            completionCallback.run();
                        }
                    } catch (Exception e) {
                        System.err.println("发送线程异常: " + e.getMessage());
                        e.printStackTrace();
                        try {
                            sendMessage(session, "error", "发送语音失败: " + e.getMessage());
                        } catch (IOException ioException) {
                            System.err.println("发送错误消息失败: " + ioException.getMessage());
                        }

                        // 即使出错也要执行回调
                        if (completionCallback != null) {
                            completionCallback.run();
                        }
                    }
                });

                // 启动发送线程
                sendThread.setName("TTS-Send-Thread");
                sendThread.start();

            } catch (Exception e) {
                System.err.println("处理文本到语音失败: " + e.getMessage());
                e.printStackTrace();
                try {
                    sendMessage(session, "error", "生成语音失败: " + e.getMessage());
                } catch (IOException ioException) {
                    System.err.println("发送错误消息失败: " + ioException.getMessage());
                }
                // 即使出错也要执行回调
                if (completionCallback != null) {
                    completionCallback.run();
                }
            }
        });
    }

    /**
     * 将文本分段处理，以便更好地生成语音
     *
     * @param text 原始文本
     * @return 分段后的文本数组
     */
    private String[] segmentText(String text) {
        if (text == null || text.isEmpty()) {
            return new String[0];
        }

        // 按句子结束符号分段等
        char[] sentenceEndings = {'.', '。', '!', '！', '?', '？', '\n', ',', '，', '、', ';', '；'};
        StringBuilder currentSegment = new StringBuilder();
        java.util.List<String> segments = new java.util.ArrayList<>();

        for (int i = 0; i < text.length(); i++) {
            char c = text.charAt(i);
            currentSegment.append(c);

            boolean isEnding = false;
            for (char ending : sentenceEndings) {
                if (c == ending) {
                    isEnding = true;
                    break;
                }
            }

            // 如果遇到句子结束符，或者当前段落已经超过100个字符，就进行分段
            // 减小最大长度从150到100，使语音分段更细致
            if (isEnding || currentSegment.length() >= 50) {
                if (currentSegment.length() > 0) {
                    segments.add(currentSegment.toString().trim());
                    currentSegment = new StringBuilder();
                }
            }
        }

        // 处理最后一段
        if (currentSegment.length() > 0) {
            segments.add(currentSegment.toString().trim());
        }

        return segments.toArray(new String[0]);
    }

    /**
     * 生成语音数据
     * 
     * @param text 要转换的文本
     * @param speaker 音色名称
     * @param frameDuration 帧时长(毫秒)
     * @param segmentIndex 段落索引
     * @param totalSegments 总段落数
     * @return 生成的音频数据字节数组
     * @throws Exception 如果生成失败
     */
    public byte[] generateSpeech(String text, String speaker, int frameDuration, int segmentIndex, int totalSegments) throws Exception {
        if (text == null || text.trim().isEmpty()) {
            return new byte[0];
        }

        // 过滤表情符号
        text = filterEmoji(text);
        if (text.trim().isEmpty()) {
            System.out.println("段落 " + (segmentIndex + 1) + "/" + totalSegments + " 过滤表情符号后文本为空");
            return new byte[0];
        }

        // 构建请求参数
        Map<String, Object> requestParams = new HashMap<>();
        requestParams.put("tts_text", text);
        requestParams.put("mode", "预训练音色");  // 使用预训练模式
        requestParams.put("complexity", complexity);  // 使用配置文件中的音频质量参数
        requestParams.put("bit_rate", 24000);  // 比特率
        requestParams.put("packet_loss", 0);  // 丢包率
        requestParams.put("vbr", 1);  // 可变比特率
        requestParams.put("sft_speaker", speaker);  // 音色
        
        requestParams.put("segment_index", segmentIndex); // 添加段落索引，便于服务端优先处理
        requestParams.put("total_segments", totalSegments); // 添加总段落数
        
        // 设置帧时长
        if (frameDuration > 0) {
            requestParams.put("frame_duration", frameDuration);
        } else {
            requestParams.put("frame_duration", 60);  // 默认帧时长
        }
        
        // 设置语速
        requestParams.put("speed", 1.0);  // 默认语速

        // 构建HTTP请求头
        HttpHeaders headers = new HttpHeaders();
        headers.setContentType(MediaType.APPLICATION_JSON);
        headers.setAcceptCharset(Collections.singletonList(StandardCharsets.UTF_8));
        
        // 添加自定义头，设置处理优先级（越小越优先）
        headers.add("X-Segment-Index", String.valueOf(segmentIndex)); 
        headers.add("X-Total-Segments", String.valueOf(totalSegments));

        // 打印请求参数，方便调试
        System.out.println("发送TTS请求: " + cosyvoiceApiUrl + "/tts 段落: " + (segmentIndex + 1) + "/" + totalSegments);
        System.out.println("请求参数: " + requestParams);

        HttpEntity<Map<String, Object>> requestEntity = new HttpEntity<>(requestParams, headers);

        long startTime = System.currentTimeMillis();
        
        try {
            // 设置较长的超时时间，确保长文本也能处理完成
            int maxRetries = 2;
            byte[] responseBody = null;
            Exception lastException = null;
            
            // 重试逻辑
            for (int retry = 0; retry <= maxRetries; retry++) {
                try {
                    // 发送HTTP请求
                    ResponseEntity<byte[]> response = restTemplate.exchange(
                            cosyvoiceApiUrl + "/tts",
                            HttpMethod.POST,
                            requestEntity,
                            byte[].class
                    );

                    if (response.getStatusCode() == HttpStatus.OK) {
                        responseBody = response.getBody();
                        break;  // 请求成功，跳出重试循环
                    } else {
                        System.err.println("段落 " + (segmentIndex + 1) + "/" + totalSegments + 
                                         " 语音合成请求返回非200状态码: " + response.getStatusCode());
                        // 继续重试
                    }
                } catch (Exception e) {
                    lastException = e;
                    System.err.println("段落 " + (segmentIndex + 1) + "/" + totalSegments + 
                                     " 语音合成请求失败(尝试 " + (retry + 1) + "/" + (maxRetries + 1) + "): " + e.getMessage());
                    
                    if (retry < maxRetries) {
                        // 在重试前等待一段时间（随着重试次数增加而增加等待时间）
                        Thread.sleep(500 * (retry + 1));
                    } else {
                        // 最后一次重试失败
                        System.err.println("段落 " + (segmentIndex + 1) + "/" + totalSegments + " 所有重试都失败了");
                        throw e;
                    }
                }
            }

            // 如果响应为空，则抛出异常
            if (responseBody == null) {
                if (lastException != null) {
                    throw lastException;
                } else {
                    throw new Exception("语音合成未返回数据");
                }
            }

            // 计算处理时间
            long processTime = System.currentTimeMillis() - startTime;
            System.out.println("段落 " + (segmentIndex + 1) + "/" + totalSegments + 
                             " 生成的语音数据大小: " + responseBody.length + " 字节, 耗时: " + processTime + "ms");

            return responseBody;

        } catch (RestClientException e) {
            System.err.println("段落 " + (segmentIndex + 1) + "/" + totalSegments + " 语音合成API调用失败: " + e.getMessage());
            e.printStackTrace();
            throw new Exception("语音合成请求失败: " + e.getMessage());
        } catch (Exception e) {
            System.err.println("段落 " + (segmentIndex + 1) + "/" + totalSegments + " 语音合成处理异常: " + e.getMessage());
            e.printStackTrace();
            throw e;
        }
    }

    /**
     * 生成语音数据（指定帧时长） - 兼容原始接口
     *
     * @param text 要转换的文本
     * @param speaker 音色名称
     * @param frameDuration 帧时长(毫秒)
     * @return 生成的音频数据字节数组
     * @throws Exception 如果生成失败
     */
    public byte[] generateSpeech(String text, String speaker, int frameDuration) throws Exception {
        return generateSpeech(text, speaker, frameDuration, 0, 1);
    }

    /**
     * 生成静音WAV数据作为备用
     * 当TTS服务不可用时使用
     */
    private byte[] generateSilentAudio() {
        try {
            // 创建一个简单的静音WAV文件
            int sampleRate = 16000;
            int numChannels = 1;
            int bitsPerSample = 16;
            int numSamples = sampleRate; // 1秒静音

            // WAV文件头大小
            int headerSize = 44;
            int dataSize = numSamples * numChannels * bitsPerSample / 8;
            int fileSize = headerSize + dataSize;

            // 创建字节数组
            byte[] buffer = new byte[fileSize];

            // 填充WAV头
            // "RIFF"
            buffer[0] = 'R';
            buffer[1] = 'I';
            buffer[2] = 'F';
            buffer[3] = 'F';

            // 文件大小 - 8
            int fileSizeMinus8 = fileSize - 8;
            buffer[4] = (byte) (fileSizeMinus8 & 0xFF);
            buffer[5] = (byte) ((fileSizeMinus8 >> 8) & 0xFF);
            buffer[6] = (byte) ((fileSizeMinus8 >> 16) & 0xFF);
            buffer[7] = (byte) ((fileSizeMinus8 >> 24) & 0xFF);

            // "WAVE"
            buffer[8] = 'W';
            buffer[9] = 'A';
            buffer[10] = 'V';
            buffer[11] = 'E';

            // "fmt "
            buffer[12] = 'f';
            buffer[13] = 'm';
            buffer[14] = 't';
            buffer[15] = ' ';

            // 子块大小
            buffer[16] = 16;
            buffer[17] = 0;
            buffer[18] = 0;
            buffer[19] = 0;

            // 音频格式 (PCM = 1)
            buffer[20] = 1;
            buffer[21] = 0;

            // 声道数
            buffer[22] = (byte) numChannels;
            buffer[23] = 0;

            // 采样率
            buffer[24] = (byte) (sampleRate & 0xFF);
            buffer[25] = (byte) ((sampleRate >> 8) & 0xFF);
            buffer[26] = (byte) ((sampleRate >> 16) & 0xFF);
            buffer[27] = (byte) ((sampleRate >> 24) & 0xFF);

            // 字节率 (SampleRate * NumChannels * BitsPerSample/8)
            int byteRate = sampleRate * numChannels * bitsPerSample / 8;
            buffer[28] = (byte) (byteRate & 0xFF);
            buffer[29] = (byte) ((byteRate >> 8) & 0xFF);
            buffer[30] = (byte) ((byteRate >> 16) & 0xFF);
            buffer[31] = (byte) ((byteRate >> 24) & 0xFF);

            // 块对齐 (NumChannels * BitsPerSample/8)
            short blockAlign = (short) (numChannels * bitsPerSample / 8);
            buffer[32] = (byte) (blockAlign & 0xFF);
            buffer[33] = (byte) ((blockAlign >> 8) & 0xFF);

            // 每样本位数
            buffer[34] = (byte) bitsPerSample;
            buffer[35] = 0;

            // "data"
            buffer[36] = 'd';
            buffer[37] = 'a';
            buffer[38] = 't';
            buffer[39] = 'a';

            // 数据大小
            buffer[40] = (byte) (dataSize & 0xFF);
            buffer[41] = (byte) ((dataSize >> 8) & 0xFF);
            buffer[42] = (byte) ((dataSize >> 16) & 0xFF);
            buffer[43] = (byte) ((dataSize >> 24) & 0xFF);

            // 数据部分全部为0，表示静音
            // buffer[44] 到 buffer[fileSize-1] 默认就是0，无需特别设置

            System.out.println("生成静音音频数据: " + buffer.length + " 字节");
            return buffer;
        } catch (Exception e) {
            System.err.println("生成静音音频失败: " + e.getMessage());
            e.printStackTrace();

            // 如果生成静音也失败，返回一个最小的有效WAV文件
            byte[] minimal = new byte[44 + 32];  // 44字节头部 + 32字节静音数据
            // 设置基本的WAV头
            minimal[0] = 'R'; minimal[1] = 'I'; minimal[2] = 'F'; minimal[3] = 'F';
            minimal[8] = 'W'; minimal[9] = 'A'; minimal[10] = 'V'; minimal[11] = 'E';
            minimal[12] = 'f'; minimal[13] = 'm'; minimal[14] = 't'; minimal[15] = ' ';
            minimal[16] = 16; // 子块大小
            minimal[20] = 1;  // PCM格式
            minimal[22] = 1;  // 单声道
            minimal[24] = 0x10; minimal[25] = 0x27; // 10000Hz采样率
            minimal[34] = 16; // 16位
            minimal[36] = 'd'; minimal[37] = 'a'; minimal[38] = 't'; minimal[39] = 'a';
            minimal[40] = 32; // 数据大小32字节

            return minimal;
        }
    }

    /**
     * 发送文本消息到WebSocket
     */
    private void sendMessage(WebSocketSession session, String type, String content) throws IOException {
        if (session != null && session.isOpen()) {
            Map<String, String> response = new HashMap<>();
            response.put("type", type);
            response.put("content", content);

            String json = objectMapper.writeValueAsString(response);
            synchronized (session) {
                session.sendMessage(new TextMessage(json));
            }
        } else {
            System.err.println("无法发送消息：WebSocket会话已关闭或为null");
        }
    }

    /**
     * 发送二进制音频数据到WebSocket，附带元数据
     */
    private void sendBinaryMessage(WebSocketSession session, Map<String, Object> metadata, byte[] audioData) throws IOException {
        if (session != null && session.isOpen()) {
            // 先发送元数据
            String metadataJson = objectMapper.writeValueAsString(metadata);
            synchronized (session) {
                session.sendMessage(new TextMessage(metadataJson));

                // 然后发送二进制数据
                org.springframework.web.socket.BinaryMessage binaryMessage =
                    new org.springframework.web.socket.BinaryMessage(audioData);
                session.sendMessage(binaryMessage);
            }
        } else {
            System.err.println("无法发送二进制消息：WebSocket会话已关闭或为null");
        }
    }

    /**
     * 在服务关闭时释放资源
     */
    @PreDestroy
    public void shutdown() {
        System.out.println("正在关闭CosyvoiceService...");

        // 关闭线程池
        try {
            executorService.shutdown();
            if (!executorService.awaitTermination(5, java.util.concurrent.TimeUnit.SECONDS)) {
                executorService.shutdownNow();
            }
        } catch (InterruptedException e) {
            executorService.shutdownNow();
            Thread.currentThread().interrupt();
        }

        System.out.println("CosyvoiceService已关闭");
    }

    /**
     * 异步处理单个段落
     */
    private void processSegmentAsync(String text, String speaker, Consumer<ProcessedSegment> callback) {
        executorService.submit(() -> {
            try {
                // 段落预处理时间统计
                long startTime = System.currentTimeMillis();

                // 如果文本为空或只包含表情符号，则跳过处理
                if (text == null || text.trim().isEmpty() || containsOnlyEmoji(text)) {
                    System.out.println("段落文本为空或只包含表情符号，跳过处理: " + text);
                    callback.accept(new ProcessedSegment(text, new byte[0], -1));
                    return;
                }

                System.out.println("开始处理段落: " + (text.length() > 30 ? text.substring(0, 27) + "..." : text));

                // 调用TTS API生成语音
                byte[] audioData = generateSpeech(text, speaker);

                long processTime = System.currentTimeMillis() - startTime;

                if (audioData != null && audioData.length > 0) {
                    // 创建处理好的段落对象
                    ProcessedSegment segment = new ProcessedSegment(text, audioData, -1);
                    System.out.println("段落处理完成, 字节数: " + audioData.length + ", 耗时: " + processTime + "ms");

                    // 调用回调
                    callback.accept(segment);
                } else {
                    System.err.println("生成的音频数据为空, 耗时: " + processTime + "ms");
                    // 即使生成失败，也需要调用回调以确保处理流程继续
                    callback.accept(new ProcessedSegment(text, new byte[0], -1));
                }
            } catch (Exception e) {
                System.err.println("处理段落失败: " + e.getMessage());
                e.printStackTrace();
                // 即使出错，也需要调用回调以确保处理流程继续
                callback.accept(new ProcessedSegment(text, new byte[0], -1));
            }
        });
    }

    /**
     * 表示已处理完成的段落
     */
    private static class ProcessedSegment {
        final String text;
        final byte[] audioData;
        int index;  // 段落索引，-1表示尚未分配

        ProcessedSegment(String text, byte[] audioData, int index) {
            this.text = text;
            this.audioData = audioData;
            this.index = index;
        }
    }

    /**
     * 过滤表情符号
     */
    private String filterEmoji(String input) {
        if (input == null || input.isEmpty()) {
            return input;
        }

        // 如果输入字符串只包含表情符号，返回空字符串而不是抛出异常
        if (containsOnlyEmoji(input)) {
            System.out.println("文本仅包含表情符号，跳过TTS处理: " + input);
            return "";
        }

        try {
            // 使用正则表达式移除表情符号
            Pattern pattern = Pattern.compile(
                "[\\ud83c\\udc00-\\ud83c\\udfff]|[\\ud83d\\udc00-\\ud83d\\udfff]|[\\u2600-\\u27ff]",
                Pattern.UNICODE_CASE | Pattern.CASE_INSENSITIVE
            );
            java.util.regex.Matcher matcher = pattern.matcher(input);
            String result = matcher.replaceAll("");

            // 如果过滤后字符串为空或只有空白，返回原文本的首个非表情字符
            if (result.trim().isEmpty() && !input.trim().isEmpty()) {
                for (char c : input.toCharArray()) {
                    if (!isEmoji(c)) {
                        return String.valueOf(c);
                    }
                }
                // 如果所有字符都是表情符号，返回空字符串
                return "";
            }

            return result;
        } catch (Exception e) {
            System.err.println("过滤表情符号时发生错误: " + e.getMessage());
            e.printStackTrace();
            return input;  // 出错时返回原始输入
        }
    }

    /**
     * 检查字符是否为表情符号
     */
    private boolean isEmoji(char c) {
        return (c >= '\uD83C' && c <= '\uD83E') || // SMP (Supplementary Multilingual Plane)
               (c >= '\u2600' && c <= '\u27BF');   // 其他常见表情符号范围
    }

    /**
     * 检查字符串是否只包含表情符号和空白字符
     */
    private boolean containsOnlyEmoji(String input) {
        if (input == null || input.trim().isEmpty()) {
            return false;
        }

        for (char c : input.toCharArray()) {
            if (!Character.isWhitespace(c) && !isEmoji(c)) {
                return false;
            }
        }
        return true;
    }

    /**
     * 直接生成语音数据，使用默认帧时长60ms
     *
     * @param text 要转换的文本
     * @param speaker 音色名称
     * @return Opus格式的音频数据
     * @throws Exception 如果生成过程中出错
     */
    public byte[] generateSpeech(String text, String speaker) throws Exception {
        return generateSpeech(text, speaker, 60, 0, 1); // 默认使用60ms帧时长
    }

    /**
     * 获取CosyVoice API的URL
     * @return API URL
     */
    public String getCosyvoiceApiUrl() {
        return cosyvoiceApiUrl;
    }
} 