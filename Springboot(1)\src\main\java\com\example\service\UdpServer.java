package com.example.service;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import jakarta.annotation.PostConstruct;
import jakarta.annotation.PreDestroy;
import javax.crypto.Cipher;
import javax.crypto.spec.SecretKeySpec;
import java.io.ByteArrayOutputStream;
import java.io.IOException;
import java.net.DatagramPacket;
import java.net.DatagramSocket;
import java.net.InetAddress;
import java.net.SocketException;
import java.nio.ByteBuffer;
import java.nio.ByteOrder;
import java.util.Arrays;
import java.util.Map;
import java.util.concurrent.*;

/**
 * UDP服务器，处理与ESP32设备的音频数据流
 */
@Service
public class UdpServer {

    @Value("${udp.server.port:12345}")
    private int port;

    private DatagramSocket socket;
    private final ExecutorService executorService = Executors.newCachedThreadPool();
    private final ConcurrentMap<String, UdpSession> sessions = new ConcurrentHashMap<>();
    private final AudioCodec audioCodec;
    private boolean running = false;

    @Autowired
    public UdpServer(AudioCodec audioCodec) {
        this.audioCodec = audioCodec;
    }

    @PostConstruct
    public void init() {
        try {
            // 创建UDP socket
            socket = new DatagramSocket(port);
            running = true;
            System.out.println("UDP服务器启动，监听端口: " + port);

            // 启动接收线程
            executorService.submit(this::receiveLoop);
        } catch (SocketException e) {
            System.err.println("UDP服务器启动失败: " + e.getMessage());
            e.printStackTrace();
        }
    }

    /**
     * UDP数据接收循环
     */
    private void receiveLoop() {
        byte[] buffer = new byte[4096]; // 4KB缓冲区
        
        while (running) {
            try {
                DatagramPacket packet = new DatagramPacket(buffer, buffer.length);
                
                // 阻塞等待接收数据
                socket.receive(packet);
                
                // 提取客户端信息
                InetAddress clientAddress = packet.getAddress();
                int clientPort = packet.getPort();
                String clientId = clientAddress.getHostAddress() + ":" + clientPort;
                
                // 处理接收到的数据
                byte[] data = Arrays.copyOf(packet.getData(), packet.getLength());
                processIncomingData(clientId, clientAddress, clientPort, data);
            } catch (IOException e) {
                if (!running) {
                    // 如果服务器已关闭，退出循环
                    break;
                }
                System.err.println("接收UDP数据错误: " + e.getMessage());
            }
        }
    }

    /**
     * 处理接收到的UDP数据
     */
    private void processIncomingData(String clientId, InetAddress clientAddress, int clientPort, byte[] data) {
        // 查找或创建会话
        UdpSession session = sessions.get(clientId);
        if (session == null) {
            System.out.println("未找到UDP会话: " + clientId);
            return;
        }
        
        try {
            // 处理数据
            session.processIncomingData(data);
        } catch (Exception e) {
            System.err.println("处理UDP数据错误: " + e.getMessage());
            e.printStackTrace();
        }
    }

    /**
     * 创建新的UDP会话
     */
    public UdpSession createSession(String sessionId, String clientId, String key, String nonce) {
        UdpSession session = new UdpSession(sessionId, clientId, key, nonce);
        sessions.put(clientId, session);
        return session;
    }

    /**
     * 关闭UDP会话
     */
    public void closeSession(String clientId) {
        UdpSession session = sessions.remove(clientId);
        if (session != null) {
            System.out.println("关闭UDP会话: " + clientId);
        }
    }

    @PreDestroy
    public void shutdown() {
        System.out.println("关闭UDP服务器...");
        
        // 停止运行标志
        running = false;
        
        // 关闭所有会话
        for (String clientId : sessions.keySet()) {
            closeSession(clientId);
        }
        
        // 关闭Socket
        if (socket != null && !socket.isClosed()) {
            socket.close();
        }
        
        // 关闭线程池
        executorService.shutdown();
        try {
            if (!executorService.awaitTermination(5, TimeUnit.SECONDS)) {
                executorService.shutdownNow();
            }
        } catch (InterruptedException e) {
            executorService.shutdownNow();
        }
        
        System.out.println("UDP服务器已关闭");
    }

    /**
     * UDP会话类，处理与单个客户端的通信
     */
    public class UdpSession {
        private final String sessionId;
        private final String clientId;
        private InetAddress clientAddress;
        private int clientPort;
        private final byte[] key;
        private final byte[] nonce;
        private int localSequence = 0;
        private int remoteSequence = 0;

        public UdpSession(String sessionId, String clientId, String key, String nonce) {
            this.sessionId = sessionId;
            this.clientId = clientId;
            this.key = hexStringToByteArray(key);
            this.nonce = hexStringToByteArray(nonce);
            
            // 从clientId中提取地址和端口
            String[] parts = clientId.split(":");
            try {
                this.clientAddress = InetAddress.getByName(parts[0]);
                this.clientPort = Integer.parseInt(parts[1]);
            } catch (Exception e) {
                System.err.println("解析客户端地址失败: " + e.getMessage());
            }
        }

        /**
         * 处理接收到的数据
         */
        public void processIncomingData(byte[] encryptedData) throws Exception {
            if (encryptedData.length < nonce.length) {
                throw new IllegalArgumentException("数据太短，无法解密");
            }
            
            // 解密数据
            byte[] decryptedData = decrypt(encryptedData);
            
            // 处理解密后的数据
            // TODO: 实现实际的音频处理逻辑
            
            // 更新远程序列号
            remoteSequence++;
        }

        /**
         * 发送数据到客户端
         */
        public void sendData(byte[] data) throws Exception {
            if (clientAddress == null) {
                throw new IllegalStateException("客户端地址未设置");
            }
            
            // 准备nonce
            ByteBuffer nonceBuffer = ByteBuffer.allocate(nonce.length);
            nonceBuffer.order(ByteOrder.LITTLE_ENDIAN);
            nonceBuffer.put(nonce, 0, nonce.length);
            
            // 设置数据大小 (2-3字节)
            nonceBuffer.putShort(2, (short)data.length);
            
            // 设置时间戳 (8-11字节)
            nonceBuffer.putInt(8, (int)(System.currentTimeMillis() / 1000));
            
            // 设置序列号 (12-15字节)
            nonceBuffer.putInt(12, ++localSequence);
            
            // 加密数据
            byte[] encryptedData = encrypt(nonceBuffer.array(), data);
            
            // 发送数据包
            DatagramPacket packet = new DatagramPacket(encryptedData, encryptedData.length, clientAddress, clientPort);
            socket.send(packet);
            
            // 保存音频数据到文件
            try {
                java.io.File dir = new java.io.File("D:/ESP32/Springboot");
                if (!dir.exists()) {
                    dir.mkdirs();
                }
                java.io.FileOutputStream fos = new java.io.FileOutputStream("D:/ESP32/Springboot/last_sent_audio.pcm");
                fos.write(data);
                fos.close();
            } catch (java.io.IOException e) {
                System.err.println("保存音频文件失败: " + e.getMessage());
            }
        }

        /**
         * 加密数据
         */
        private byte[] encrypt(byte[] nonce, byte[] plaintext) throws Exception {
            try {
                // 使用AES-CTR模式加密
                Cipher cipher = Cipher.getInstance("AES/CTR/NoPadding");
                SecretKeySpec keySpec = new SecretKeySpec(key, "AES");
                
                // 使用nonce前16字节作为IV
                javax.crypto.spec.IvParameterSpec ivSpec = new javax.crypto.spec.IvParameterSpec(nonce, 0, 16);
                cipher.init(Cipher.ENCRYPT_MODE, keySpec, ivSpec);
                
                // 将nonce和加密后的数据合并
                ByteArrayOutputStream outputStream = new ByteArrayOutputStream();
                outputStream.write(nonce);
                outputStream.write(cipher.doFinal(plaintext));
                
                return outputStream.toByteArray();
            } catch (Exception e) {
                System.err.println("加密失败: " + e.getMessage());
                throw e;
            }
        }

        /**
         * 解密数据
         */
        private byte[] decrypt(byte[] encryptedData) throws Exception {
            try {
                // 提取nonce (前16字节)
                byte[] packetNonce = Arrays.copyOfRange(encryptedData, 0, 16);
                
                // 提取加密数据
                byte[] ciphertext = Arrays.copyOfRange(encryptedData, 16, encryptedData.length);
                
                // 使用AES-CTR模式解密
                Cipher cipher = Cipher.getInstance("AES/CTR/NoPadding");
                SecretKeySpec keySpec = new SecretKeySpec(key, "AES");
                
                // 使用nonce作为IV
                javax.crypto.spec.IvParameterSpec ivSpec = new javax.crypto.spec.IvParameterSpec(packetNonce, 0, 16);
                cipher.init(Cipher.DECRYPT_MODE, keySpec, ivSpec);
                
                return cipher.doFinal(ciphertext);
            } catch (Exception e) {
                System.err.println("解密失败: " + e.getMessage());
                throw e;
            }
        }
    }
    
    /**
     * 将十六进制字符串转换为字节数组
     */
    private byte[] hexStringToByteArray(String hex) {
        int len = hex.length();
        byte[] data = new byte[len / 2];
        for (int i = 0; i < len; i += 2) {
            data[i / 2] = (byte) ((Character.digit(hex.charAt(i), 16) << 4)
                    + Character.digit(hex.charAt(i + 1), 16));
        }
        return data;
    }
} 